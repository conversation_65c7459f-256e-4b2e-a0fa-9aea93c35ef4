#工程基本信息
set(PROJECT_NAME write_cmake_test)          #工程名字
set(CMAKE_C_STANDARD 11)                    #C语言标准
set(CMAKE_ASM_STANDARD_REQUIRED ON)         #开启汇编
set(CMAKE_C_EXTENSIONS ON)                  #开启C++
set(CMAKE_BUILD_TYPE Debug)                 #编译类型：debug
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)       #生成compile_commands.json文件
cmake_minimum_required(VERSION 3.22)        #cmake版本
enable_language(C ASM CXX)                  #编程语言
set(CMAKE_EXECUTABLE_SUFFIX    ".elf")      #可执行文件后缀
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/output) #可执行文件输出路径

project(${PROJECT_NAME})                             #创建工程


#收集源文件，头文件目录
add_executable(${PROJECT_NAME})


file(GLOB_RECURSE user_source "${CMAKE_SOURCE_DIR}/Core/Src/*.c")

set(INCLUDE_DIRECTORY 
    "${CMAKE_SOURCE_DIR}/Core/App/Inc"
    "${CMAKE_SOURCE_DIR}/Core/User/Inc"
    "${CMAKE_SOURCE_DIR}/Core/Components/Inc"
)

target_sources(${PROJECT_NAME} PRIVATE
    ${user_source}
    ${CMAKE_SOURCE_DIR}/startup_stm32f401xc.s
)

target_include_directories(${PROJECT_NAME} PRIVATE ${INCLUDE_DIRECTORY})

#设置编译选项
set(CMAKE_SYSTEM_NAME               Generic)
set(CMAKE_SYSTEM_PROCESSOR          arm)

set(CMAKE_C_COMPILER                arm-none-eabi-gcc)
set(CMAKE_ASM_COMPILER              arm-none-eabi-gcc)
set(CMAKE_CXX_COMPILER              arm-none-eabi-g++)
set(CMAKE_LINKER                    arm-none-eabi-g++)
set(CMAKE_OBJCOPY                   arm-none-eabi-objcopy)
set(CMAKE_SIZE                      arm-none-eabi-size)


add_compile_definitions(
    USE_HAL_DRIVER
    STM32F401xC
)


set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb")      #架构和浮点运算相关选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -Wpedantic")                                        #警告和调试相关选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -O0 -g3")                                                         #优化和调试相关选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fdata-sections -ffunction-sections")                             #代码和数据优化选项 


set(CMAKE_CXX_FLAGS "${CMAKE_C_FLAGS} -fno-rtti -fno-exceptions -fno-threadsafe-statics")               #C++ 特定选项


set(CMAKE_ASM_FLAGS "${CMAKE_C_FLAGS} -x assembler-with-cpp -MMD -MP")                                  #汇编器相关选项



set(CMAKE_C_LINK_FLAGS "${CMAKE_C_LINK_FLAGS} -T \"${CMAKE_SOURCE_DIR}/STM32F401CCUx_FLASH.ld\"")         #链接脚本
set(CMAKE_C_LINK_FLAGS "${CMAKE_C_LINK_FLAGS} --specs=nano.specs")                                     #用newlib库
set(CMAKE_C_LINK_FLAGS "${CMAKE_C_LINK_FLAGS} -Wl,-Map=asm/${CMAKE_PROJECT_NAME}.map")                     #生成map文件
set(CMAKE_C_LINK_FLAGS "${CMAKE_C_LINK_FLAGS} -Wl,--gc-sections")                                      #多余段回收
set(CMAKE_C_LINK_FLAGS "${CMAKE_C_LINK_FLAGS} -Wl,--print-memory-usage")                               #打印内存

set(CMAKE_CXX_LINK_FLAGS "${CMAKE_CXX_LINK_FLAGS} -Wl,--start-group -lc -lm -lstdc++ -lsupc++ -Wl,--end-group") #链接c c++  数学库


#转化成bin，hex
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_OBJCOPY} -O binary ${CMAKE_SOURCE_DIR}/output/$<TARGET_FILE:${PROJECT_NAME}> ${CMAKE_SOURCE_DIR}/output/${PROJECT_NAME}.bin
    COMMAND ${CMAKE_OBJCOPY} -O ihex ${CMAKE_SOURCE_DIR}/output/$<TARGET_FILE:${PROJECT_NAME}> ${CMAKE_SOURCE_DIR}/output/${PROJECT_NAME}.hex
    COMMAND arm-none-eabi-objdump -d ${CMAKE_SOURCE_DIR}/output/$<TARGET_FILE:${PROJECT_NAME}> > ${CMAKE_SOURCE_DIR}/output/${PROJECT_NAME}.s
    COMMAND echo "ninja 编译成功！"
)
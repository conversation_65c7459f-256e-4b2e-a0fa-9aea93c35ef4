
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:9 (enable_language)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeNinjaFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Program used to build from build.ninja files."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ninja-build"
      - "ninja"
      - "samu"
    candidate_directories:
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "C:/Program Files/PowerShell/7/ninja-build.com"
      - "C:/Program Files/PowerShell/7/ninja-build.exe"
      - "C:/Program Files/PowerShell/7/ninja-build"
      - "C:/Program Files/PowerShell/7/ninja.com"
      - "C:/Program Files/PowerShell/7/ninja.exe"
      - "C:/Program Files/PowerShell/7/ninja"
      - "C:/Program Files/PowerShell/7/samu.com"
      - "C:/Program Files/PowerShell/7/samu.exe"
      - "C:/Program Files/PowerShell/7/samu"
      - "D:/APP/Vmware/bin/ninja-build.com"
      - "D:/APP/Vmware/bin/ninja-build.exe"
      - "D:/APP/Vmware/bin/ninja-build"
      - "D:/APP/Vmware/bin/ninja.com"
      - "D:/APP/Vmware/bin/ninja.exe"
      - "D:/APP/Vmware/bin/ninja"
      - "D:/APP/Vmware/bin/samu.com"
      - "D:/APP/Vmware/bin/samu.exe"
      - "D:/APP/Vmware/bin/samu"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja-build.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja-build.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja-build"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/samu.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/samu.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/samu"
      - "C:/Windows/System32/ninja-build.com"
      - "C:/Windows/System32/ninja-build.exe"
      - "C:/Windows/System32/ninja-build"
      - "C:/Windows/System32/ninja.com"
      - "C:/Windows/System32/ninja.exe"
      - "C:/Windows/System32/ninja"
      - "C:/Windows/System32/samu.com"
      - "C:/Windows/System32/samu.exe"
      - "C:/Windows/System32/samu"
      - "C:/Windows/ninja-build.com"
      - "C:/Windows/ninja-build.exe"
      - "C:/Windows/ninja-build"
      - "C:/Windows/ninja.com"
      - "C:/Windows/ninja.exe"
      - "C:/Windows/ninja"
      - "C:/Windows/samu.com"
      - "C:/Windows/samu.exe"
      - "C:/Windows/samu"
      - "C:/Windows/System32/wbem/ninja-build.com"
      - "C:/Windows/System32/wbem/ninja-build.exe"
      - "C:/Windows/System32/wbem/ninja-build"
      - "C:/Windows/System32/wbem/ninja.com"
      - "C:/Windows/System32/wbem/ninja.exe"
      - "C:/Windows/System32/wbem/ninja"
      - "C:/Windows/System32/wbem/samu.com"
      - "C:/Windows/System32/wbem/samu.exe"
      - "C:/Windows/System32/wbem/samu"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja-build.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja-build.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja-build"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/samu.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/samu.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/samu"
      - "C:/Windows/System32/OpenSSH/ninja-build.com"
      - "C:/Windows/System32/OpenSSH/ninja-build.exe"
      - "C:/Windows/System32/OpenSSH/ninja-build"
      - "C:/Windows/System32/OpenSSH/ninja.com"
      - "C:/Windows/System32/OpenSSH/ninja.exe"
      - "C:/Windows/System32/OpenSSH/ninja"
      - "C:/Windows/System32/OpenSSH/samu.com"
      - "C:/Windows/System32/OpenSSH/samu.exe"
      - "C:/Windows/System32/OpenSSH/samu"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja-build.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja-build.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja-build"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/samu.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/samu.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/samu"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja-build.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja-build.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja-build"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/samu.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/samu.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/samu"
      - "D:/APP/Microsoft VS Code/bin/ninja-build.com"
      - "D:/APP/Microsoft VS Code/bin/ninja-build.exe"
      - "D:/APP/Microsoft VS Code/bin/ninja-build"
      - "D:/APP/Microsoft VS Code/bin/ninja.com"
      - "D:/APP/Microsoft VS Code/bin/ninja.exe"
      - "D:/APP/Microsoft VS Code/bin/ninja"
      - "D:/APP/Microsoft VS Code/bin/samu.com"
      - "D:/APP/Microsoft VS Code/bin/samu.exe"
      - "D:/APP/Microsoft VS Code/bin/samu"
      - "D:/APP/mingw64/bin/ninja-build.com"
      - "D:/APP/mingw64/bin/ninja-build.exe"
      - "D:/APP/mingw64/bin/ninja-build"
      - "D:/APP/mingw64/bin/ninja.com"
      - "D:/APP/mingw64/bin/ninja.exe"
      - "D:/APP/mingw64/bin/ninja"
      - "D:/APP/mingw64/bin/samu.com"
      - "D:/APP/mingw64/bin/samu.exe"
      - "D:/APP/mingw64/bin/samu"
      - "C:/Program Files/nodejs/ninja-build.com"
      - "C:/Program Files/nodejs/ninja-build.exe"
      - "C:/Program Files/nodejs/ninja-build"
      - "C:/Program Files/nodejs/ninja.com"
      - "C:/Program Files/nodejs/ninja.exe"
      - "C:/Program Files/nodejs/ninja"
      - "C:/Program Files/nodejs/samu.com"
      - "C:/Program Files/nodejs/samu.exe"
      - "C:/Program Files/nodejs/samu"
      - "D:/APP/MATLAB/bin/ninja-build.com"
      - "D:/APP/MATLAB/bin/ninja-build.exe"
      - "D:/APP/MATLAB/bin/ninja-build"
      - "D:/APP/MATLAB/bin/ninja.com"
      - "D:/APP/MATLAB/bin/ninja.exe"
      - "D:/APP/MATLAB/bin/ninja"
      - "D:/APP/MATLAB/bin/samu.com"
      - "D:/APP/MATLAB/bin/samu.exe"
      - "D:/APP/MATLAB/bin/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu"
      - "C:/Program Files/Git/cmd/ninja-build.com"
      - "C:/Program Files/Git/cmd/ninja-build.exe"
      - "C:/Program Files/Git/cmd/ninja-build"
      - "C:/Program Files/Git/cmd/ninja.com"
      - "C:/Program Files/Git/cmd/ninja.exe"
      - "C:/Program Files/Git/cmd/ninja"
      - "C:/Program Files/Git/cmd/samu.com"
      - "C:/Program Files/Git/cmd/samu.exe"
      - "C:/Program Files/Git/cmd/samu"
      - "C:/Program Files/dotnet/ninja-build.com"
      - "C:/Program Files/dotnet/ninja-build.exe"
      - "C:/Program Files/dotnet/ninja-build"
      - "C:/Program Files/dotnet/ninja.com"
      - "C:/Program Files/dotnet/ninja.exe"
      - "C:/Program Files/dotnet/ninja"
      - "C:/Program Files/dotnet/samu.com"
      - "C:/Program Files/dotnet/samu.exe"
      - "C:/Program Files/dotnet/samu"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu"
      - "D:/APP/clion/CLion_2025_1/bin/ninja-build.com"
      - "D:/APP/clion/CLion_2025_1/bin/ninja-build.exe"
      - "D:/APP/clion/CLion_2025_1/bin/ninja-build"
      - "D:/APP/clion/CLion_2025_1/bin/ninja.com"
      - "D:/APP/clion/CLion_2025_1/bin/ninja.exe"
      - "D:/APP/clion/CLion_2025_1/bin/ninja"
      - "D:/APP/clion/CLion_2025_1/bin/samu.com"
      - "D:/APP/clion/CLion_2025_1/bin/samu.exe"
      - "D:/APP/clion/CLion_2025_1/bin/samu"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja-build.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja-build.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja-build"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/samu.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/samu.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/samu"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja-build.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja-build.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja-build"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/samu.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/samu.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/samu"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja-build.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja-build.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja-build"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/samu.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/samu.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/samu"
      - "D:/APP/ninja/ninja-win/ninja-build.com"
      - "D:/APP/ninja/ninja-win/ninja-build.exe"
      - "D:/APP/ninja/ninja-win/ninja-build"
      - "D:/APP/ninja/ninja-win/ninja.com"
    found: "D:/APP/ninja/ninja-win/ninja.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:63 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:64 (_cmake_find_compiler)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_C_COMPILER"
    description: "C compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: false
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "cc"
      - "gcc"
      - "cl"
      - "bcc"
      - "xlc"
      - "icx"
      - "clang"
    candidate_directories:
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "C:/Program Files/PowerShell/7/cc.com"
      - "C:/Program Files/PowerShell/7/cc.exe"
      - "C:/Program Files/PowerShell/7/cc"
      - "C:/Program Files/PowerShell/7/gcc.com"
      - "C:/Program Files/PowerShell/7/gcc.exe"
      - "C:/Program Files/PowerShell/7/gcc"
      - "C:/Program Files/PowerShell/7/cl.com"
      - "C:/Program Files/PowerShell/7/cl.exe"
      - "C:/Program Files/PowerShell/7/cl"
      - "C:/Program Files/PowerShell/7/bcc.com"
      - "C:/Program Files/PowerShell/7/bcc.exe"
      - "C:/Program Files/PowerShell/7/bcc"
      - "C:/Program Files/PowerShell/7/xlc.com"
      - "C:/Program Files/PowerShell/7/xlc.exe"
      - "C:/Program Files/PowerShell/7/xlc"
      - "C:/Program Files/PowerShell/7/icx.com"
      - "C:/Program Files/PowerShell/7/icx.exe"
      - "C:/Program Files/PowerShell/7/icx"
      - "C:/Program Files/PowerShell/7/clang.com"
      - "C:/Program Files/PowerShell/7/clang.exe"
      - "C:/Program Files/PowerShell/7/clang"
      - "D:/APP/Vmware/bin/cc.com"
      - "D:/APP/Vmware/bin/cc.exe"
      - "D:/APP/Vmware/bin/cc"
      - "D:/APP/Vmware/bin/gcc.com"
      - "D:/APP/Vmware/bin/gcc.exe"
      - "D:/APP/Vmware/bin/gcc"
      - "D:/APP/Vmware/bin/cl.com"
      - "D:/APP/Vmware/bin/cl.exe"
      - "D:/APP/Vmware/bin/cl"
      - "D:/APP/Vmware/bin/bcc.com"
      - "D:/APP/Vmware/bin/bcc.exe"
      - "D:/APP/Vmware/bin/bcc"
      - "D:/APP/Vmware/bin/xlc.com"
      - "D:/APP/Vmware/bin/xlc.exe"
      - "D:/APP/Vmware/bin/xlc"
      - "D:/APP/Vmware/bin/icx.com"
      - "D:/APP/Vmware/bin/icx.exe"
      - "D:/APP/Vmware/bin/icx"
      - "D:/APP/Vmware/bin/clang.com"
      - "D:/APP/Vmware/bin/clang.exe"
      - "D:/APP/Vmware/bin/clang"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cc.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cc.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cc"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cl.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cl.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/cl"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/bcc.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/bcc.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/bcc"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/xlc.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/xlc.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/xlc"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/icx.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/icx.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/icx"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/clang.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/clang.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/clang"
      - "C:/Windows/System32/cc.com"
      - "C:/Windows/System32/cc.exe"
      - "C:/Windows/System32/cc"
      - "C:/Windows/System32/gcc.com"
      - "C:/Windows/System32/gcc.exe"
      - "C:/Windows/System32/gcc"
      - "C:/Windows/System32/cl.com"
      - "C:/Windows/System32/cl.exe"
      - "C:/Windows/System32/cl"
      - "C:/Windows/System32/bcc.com"
      - "C:/Windows/System32/bcc.exe"
      - "C:/Windows/System32/bcc"
      - "C:/Windows/System32/xlc.com"
      - "C:/Windows/System32/xlc.exe"
      - "C:/Windows/System32/xlc"
      - "C:/Windows/System32/icx.com"
      - "C:/Windows/System32/icx.exe"
      - "C:/Windows/System32/icx"
      - "C:/Windows/System32/clang.com"
      - "C:/Windows/System32/clang.exe"
      - "C:/Windows/System32/clang"
      - "C:/Windows/cc.com"
      - "C:/Windows/cc.exe"
      - "C:/Windows/cc"
      - "C:/Windows/gcc.com"
      - "C:/Windows/gcc.exe"
      - "C:/Windows/gcc"
      - "C:/Windows/cl.com"
      - "C:/Windows/cl.exe"
      - "C:/Windows/cl"
      - "C:/Windows/bcc.com"
      - "C:/Windows/bcc.exe"
      - "C:/Windows/bcc"
      - "C:/Windows/xlc.com"
      - "C:/Windows/xlc.exe"
      - "C:/Windows/xlc"
      - "C:/Windows/icx.com"
      - "C:/Windows/icx.exe"
      - "C:/Windows/icx"
      - "C:/Windows/clang.com"
      - "C:/Windows/clang.exe"
      - "C:/Windows/clang"
      - "C:/Windows/System32/wbem/cc.com"
      - "C:/Windows/System32/wbem/cc.exe"
      - "C:/Windows/System32/wbem/cc"
      - "C:/Windows/System32/wbem/gcc.com"
      - "C:/Windows/System32/wbem/gcc.exe"
      - "C:/Windows/System32/wbem/gcc"
      - "C:/Windows/System32/wbem/cl.com"
      - "C:/Windows/System32/wbem/cl.exe"
      - "C:/Windows/System32/wbem/cl"
      - "C:/Windows/System32/wbem/bcc.com"
      - "C:/Windows/System32/wbem/bcc.exe"
      - "C:/Windows/System32/wbem/bcc"
      - "C:/Windows/System32/wbem/xlc.com"
      - "C:/Windows/System32/wbem/xlc.exe"
      - "C:/Windows/System32/wbem/xlc"
      - "C:/Windows/System32/wbem/icx.com"
      - "C:/Windows/System32/wbem/icx.exe"
      - "C:/Windows/System32/wbem/icx"
      - "C:/Windows/System32/wbem/clang.com"
      - "C:/Windows/System32/wbem/clang.exe"
      - "C:/Windows/System32/wbem/clang"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cl.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cl.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cl"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/bcc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/bcc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/bcc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/xlc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/xlc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/xlc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/icx.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/icx.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/icx"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/clang.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/clang.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/clang"
      - "C:/Windows/System32/OpenSSH/cc.com"
      - "C:/Windows/System32/OpenSSH/cc.exe"
      - "C:/Windows/System32/OpenSSH/cc"
      - "C:/Windows/System32/OpenSSH/gcc.com"
      - "C:/Windows/System32/OpenSSH/gcc.exe"
      - "C:/Windows/System32/OpenSSH/gcc"
      - "C:/Windows/System32/OpenSSH/cl.com"
      - "C:/Windows/System32/OpenSSH/cl.exe"
      - "C:/Windows/System32/OpenSSH/cl"
      - "C:/Windows/System32/OpenSSH/bcc.com"
      - "C:/Windows/System32/OpenSSH/bcc.exe"
      - "C:/Windows/System32/OpenSSH/bcc"
      - "C:/Windows/System32/OpenSSH/xlc.com"
      - "C:/Windows/System32/OpenSSH/xlc.exe"
      - "C:/Windows/System32/OpenSSH/xlc"
      - "C:/Windows/System32/OpenSSH/icx.com"
      - "C:/Windows/System32/OpenSSH/icx.exe"
      - "C:/Windows/System32/OpenSSH/icx"
      - "C:/Windows/System32/OpenSSH/clang.com"
      - "C:/Windows/System32/OpenSSH/clang.exe"
      - "C:/Windows/System32/OpenSSH/clang"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cl.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cl.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cl"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/bcc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/bcc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/bcc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/xlc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/xlc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/xlc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/icx.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/icx.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/icx"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/clang.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/clang.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/clang"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/cc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/cc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/cc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/cl.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/cl.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/cl"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/bcc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/bcc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/bcc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/xlc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/xlc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/xlc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/icx.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/icx.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/icx"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/clang.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/clang.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/clang"
      - "D:/APP/Microsoft VS Code/bin/cc.com"
      - "D:/APP/Microsoft VS Code/bin/cc.exe"
      - "D:/APP/Microsoft VS Code/bin/cc"
      - "D:/APP/Microsoft VS Code/bin/gcc.com"
      - "D:/APP/Microsoft VS Code/bin/gcc.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc"
      - "D:/APP/Microsoft VS Code/bin/cl.com"
      - "D:/APP/Microsoft VS Code/bin/cl.exe"
      - "D:/APP/Microsoft VS Code/bin/cl"
      - "D:/APP/Microsoft VS Code/bin/bcc.com"
      - "D:/APP/Microsoft VS Code/bin/bcc.exe"
      - "D:/APP/Microsoft VS Code/bin/bcc"
      - "D:/APP/Microsoft VS Code/bin/xlc.com"
      - "D:/APP/Microsoft VS Code/bin/xlc.exe"
      - "D:/APP/Microsoft VS Code/bin/xlc"
      - "D:/APP/Microsoft VS Code/bin/icx.com"
      - "D:/APP/Microsoft VS Code/bin/icx.exe"
      - "D:/APP/Microsoft VS Code/bin/icx"
      - "D:/APP/Microsoft VS Code/bin/clang.com"
      - "D:/APP/Microsoft VS Code/bin/clang.exe"
      - "D:/APP/Microsoft VS Code/bin/clang"
      - "D:/APP/mingw64/bin/cc.com"
      - "D:/APP/mingw64/bin/cc.exe"
      - "D:/APP/mingw64/bin/cc"
      - "D:/APP/mingw64/bin/gcc.com"
    found: "D:/APP/mingw64/bin/gcc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/"
    found: "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:9 (enable_language)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: D:/APP/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/4.1.0-rc1/CompilerIdC/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/ar.com"
    found: "D:/APP/mingw64/bin/ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/ranlib.com"
    found: "D:/APP/mingw64/bin/ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/strip.com"
    found: "D:/APP/mingw64/bin/strip.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/ld.com"
    found: "D:/APP/mingw64/bin/ld.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/nm.com"
    found: "D:/APP/mingw64/bin/nm.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/objdump.com"
    found: "D:/APP/mingw64/bin/objdump.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/objcopy.com"
    found: "D:/APP/mingw64/bin/objcopy.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/readelf.com"
    found: "D:/APP/mingw64/bin/readelf.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/dlltool.com"
    found: "D:/APP/mingw64/bin/dlltool.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/addr2line.com"
    found: "D:/APP/mingw64/bin/addr2line.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/tapi.com"
      - "D:/APP/mingw64/bin/tapi.exe"
      - "D:/APP/mingw64/bin/tapi"
      - "C:/Program Files/PowerShell/7/tapi.com"
      - "C:/Program Files/PowerShell/7/tapi.exe"
      - "C:/Program Files/PowerShell/7/tapi"
      - "D:/APP/Vmware/bin/tapi.com"
      - "D:/APP/Vmware/bin/tapi.exe"
      - "D:/APP/Vmware/bin/tapi"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/tapi.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/tapi.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/tapi"
      - "C:/Windows/System32/tapi.com"
      - "C:/Windows/System32/tapi.exe"
      - "C:/Windows/System32/tapi"
      - "C:/Windows/tapi.com"
      - "C:/Windows/tapi.exe"
      - "C:/Windows/tapi"
      - "C:/Windows/System32/wbem/tapi.com"
      - "C:/Windows/System32/wbem/tapi.exe"
      - "C:/Windows/System32/wbem/tapi"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "C:/Windows/System32/OpenSSH/tapi.com"
      - "C:/Windows/System32/OpenSSH/tapi.exe"
      - "C:/Windows/System32/OpenSSH/tapi"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/tapi.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/tapi.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/tapi"
      - "D:/APP/Microsoft VS Code/bin/tapi.com"
      - "D:/APP/Microsoft VS Code/bin/tapi.exe"
      - "D:/APP/Microsoft VS Code/bin/tapi"
      - "C:/Program Files/nodejs/tapi.com"
      - "C:/Program Files/nodejs/tapi.exe"
      - "C:/Program Files/nodejs/tapi"
      - "D:/APP/MATLAB/bin/tapi.com"
      - "D:/APP/MATLAB/bin/tapi.exe"
      - "D:/APP/MATLAB/bin/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi"
      - "C:/Program Files/Git/cmd/tapi.com"
      - "C:/Program Files/Git/cmd/tapi.exe"
      - "C:/Program Files/Git/cmd/tapi"
      - "C:/Program Files/dotnet/tapi.com"
      - "C:/Program Files/dotnet/tapi.exe"
      - "C:/Program Files/dotnet/tapi"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi"
      - "D:/APP/clion/CLion_2025_1/bin/tapi.com"
      - "D:/APP/clion/CLion_2025_1/bin/tapi.exe"
      - "D:/APP/clion/CLion_2025_1/bin/tapi"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/tapi.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/tapi.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/tapi"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/tapi.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/tapi.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/tapi"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/tapi.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/tapi.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/tapi"
      - "D:/APP/ninja/ninja-win/tapi.com"
      - "D:/APP/ninja/ninja-win/tapi.exe"
      - "D:/APP/ninja/ninja-win/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-8.1"
      - "gcc-ar-8"
      - "gcc-ar8"
      - "gcc-ar"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/gcc-ar-8.1.com"
      - "D:/APP/mingw64/bin/gcc-ar-8.1.exe"
      - "D:/APP/mingw64/bin/gcc-ar-8.1"
      - "C:/Program Files/PowerShell/7/gcc-ar-8.1.com"
      - "C:/Program Files/PowerShell/7/gcc-ar-8.1.exe"
      - "C:/Program Files/PowerShell/7/gcc-ar-8.1"
      - "D:/APP/Vmware/bin/gcc-ar-8.1.com"
      - "D:/APP/Vmware/bin/gcc-ar-8.1.exe"
      - "D:/APP/Vmware/bin/gcc-ar-8.1"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8.1.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8.1.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8.1"
      - "C:/Windows/System32/gcc-ar-8.1.com"
      - "C:/Windows/System32/gcc-ar-8.1.exe"
      - "C:/Windows/System32/gcc-ar-8.1"
      - "C:/Windows/gcc-ar-8.1.com"
      - "C:/Windows/gcc-ar-8.1.exe"
      - "C:/Windows/gcc-ar-8.1"
      - "C:/Windows/System32/wbem/gcc-ar-8.1.com"
      - "C:/Windows/System32/wbem/gcc-ar-8.1.exe"
      - "C:/Windows/System32/wbem/gcc-ar-8.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.1"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8.1.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8.1.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8.1"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8.1.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8.1.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8.1"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8.1.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8.1.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8.1"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8.1.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8.1.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8.1"
      - "C:/Program Files/nodejs/gcc-ar-8.1.com"
      - "C:/Program Files/nodejs/gcc-ar-8.1.exe"
      - "C:/Program Files/nodejs/gcc-ar-8.1"
      - "D:/APP/MATLAB/bin/gcc-ar-8.1.com"
      - "D:/APP/MATLAB/bin/gcc-ar-8.1.exe"
      - "D:/APP/MATLAB/bin/gcc-ar-8.1"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.1.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.1.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.1"
      - "C:/Program Files/Git/cmd/gcc-ar-8.1.com"
      - "C:/Program Files/Git/cmd/gcc-ar-8.1.exe"
      - "C:/Program Files/Git/cmd/gcc-ar-8.1"
      - "C:/Program Files/dotnet/gcc-ar-8.1.com"
      - "C:/Program Files/dotnet/gcc-ar-8.1.exe"
      - "C:/Program Files/dotnet/gcc-ar-8.1"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.1.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.1.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.1"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8.1.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8.1.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8.1"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8.1.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8.1.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8.1"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8.1.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8.1.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8.1"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8.1.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8.1.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8.1"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8.1.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8.1.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8.1"
      - "D:/APP/ninja/ninja-win/gcc-ar-8.1.com"
      - "D:/APP/ninja/ninja-win/gcc-ar-8.1.exe"
      - "D:/APP/ninja/ninja-win/gcc-ar-8.1"
      - "D:/APP/mingw64/bin/gcc-ar-8.com"
      - "D:/APP/mingw64/bin/gcc-ar-8.exe"
      - "D:/APP/mingw64/bin/gcc-ar-8"
      - "C:/Program Files/PowerShell/7/gcc-ar-8.com"
      - "C:/Program Files/PowerShell/7/gcc-ar-8.exe"
      - "C:/Program Files/PowerShell/7/gcc-ar-8"
      - "D:/APP/Vmware/bin/gcc-ar-8.com"
      - "D:/APP/Vmware/bin/gcc-ar-8.exe"
      - "D:/APP/Vmware/bin/gcc-ar-8"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8"
      - "C:/Windows/System32/gcc-ar-8.com"
      - "C:/Windows/System32/gcc-ar-8.exe"
      - "C:/Windows/System32/gcc-ar-8"
      - "C:/Windows/gcc-ar-8.com"
      - "C:/Windows/gcc-ar-8.exe"
      - "C:/Windows/gcc-ar-8"
      - "C:/Windows/System32/wbem/gcc-ar-8.com"
      - "C:/Windows/System32/wbem/gcc-ar-8.exe"
      - "C:/Windows/System32/wbem/gcc-ar-8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8"
      - "C:/Program Files/nodejs/gcc-ar-8.com"
      - "C:/Program Files/nodejs/gcc-ar-8.exe"
      - "C:/Program Files/nodejs/gcc-ar-8"
      - "D:/APP/MATLAB/bin/gcc-ar-8.com"
      - "D:/APP/MATLAB/bin/gcc-ar-8.exe"
      - "D:/APP/MATLAB/bin/gcc-ar-8"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8"
      - "C:/Program Files/Git/cmd/gcc-ar-8.com"
      - "C:/Program Files/Git/cmd/gcc-ar-8.exe"
      - "C:/Program Files/Git/cmd/gcc-ar-8"
      - "C:/Program Files/dotnet/gcc-ar-8.com"
      - "C:/Program Files/dotnet/gcc-ar-8.exe"
      - "C:/Program Files/dotnet/gcc-ar-8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8"
      - "D:/APP/ninja/ninja-win/gcc-ar-8.com"
      - "D:/APP/ninja/ninja-win/gcc-ar-8.exe"
      - "D:/APP/ninja/ninja-win/gcc-ar-8"
      - "D:/APP/mingw64/bin/gcc-ar8.com"
      - "D:/APP/mingw64/bin/gcc-ar8.exe"
      - "D:/APP/mingw64/bin/gcc-ar8"
      - "C:/Program Files/PowerShell/7/gcc-ar8.com"
      - "C:/Program Files/PowerShell/7/gcc-ar8.exe"
      - "C:/Program Files/PowerShell/7/gcc-ar8"
      - "D:/APP/Vmware/bin/gcc-ar8.com"
      - "D:/APP/Vmware/bin/gcc-ar8.exe"
      - "D:/APP/Vmware/bin/gcc-ar8"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar8.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar8.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar8"
      - "C:/Windows/System32/gcc-ar8.com"
      - "C:/Windows/System32/gcc-ar8.exe"
      - "C:/Windows/System32/gcc-ar8"
      - "C:/Windows/gcc-ar8.com"
      - "C:/Windows/gcc-ar8.exe"
      - "C:/Windows/gcc-ar8"
      - "C:/Windows/System32/wbem/gcc-ar8.com"
      - "C:/Windows/System32/wbem/gcc-ar8.exe"
      - "C:/Windows/System32/wbem/gcc-ar8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar8"
      - "C:/Windows/System32/OpenSSH/gcc-ar8.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar8.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar8"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar8.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar8.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar8"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar8.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar8.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar8"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar8.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar8.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar8"
      - "C:/Program Files/nodejs/gcc-ar8.com"
      - "C:/Program Files/nodejs/gcc-ar8.exe"
      - "C:/Program Files/nodejs/gcc-ar8"
      - "D:/APP/MATLAB/bin/gcc-ar8.com"
      - "D:/APP/MATLAB/bin/gcc-ar8.exe"
      - "D:/APP/MATLAB/bin/gcc-ar8"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar8.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar8.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar8"
      - "C:/Program Files/Git/cmd/gcc-ar8.com"
      - "C:/Program Files/Git/cmd/gcc-ar8.exe"
      - "C:/Program Files/Git/cmd/gcc-ar8"
      - "C:/Program Files/dotnet/gcc-ar8.com"
      - "C:/Program Files/dotnet/gcc-ar8.exe"
      - "C:/Program Files/dotnet/gcc-ar8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar8"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar8.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar8.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar8"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar8.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar8.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar8"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar8.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar8.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar8"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar8.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar8.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar8"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar8.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar8.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar8"
      - "D:/APP/ninja/ninja-win/gcc-ar8.com"
      - "D:/APP/ninja/ninja-win/gcc-ar8.exe"
      - "D:/APP/ninja/ninja-win/gcc-ar8"
      - "D:/APP/mingw64/bin/gcc-ar.com"
    found: "D:/APP/mingw64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-8.1"
      - "gcc-ranlib-8"
      - "gcc-ranlib8"
      - "gcc-ranlib"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/gcc-ranlib-8.1.com"
      - "D:/APP/mingw64/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/mingw64/bin/gcc-ranlib-8.1"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8.1.com"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8.1.exe"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8.1"
      - "D:/APP/Vmware/bin/gcc-ranlib-8.1.com"
      - "D:/APP/Vmware/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/Vmware/bin/gcc-ranlib-8.1"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8.1.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8.1.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8.1"
      - "C:/Windows/System32/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/gcc-ranlib-8.1"
      - "C:/Windows/gcc-ranlib-8.1.com"
      - "C:/Windows/gcc-ranlib-8.1.exe"
      - "C:/Windows/gcc-ranlib-8.1"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.1"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8.1"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8.1.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8.1.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8.1"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8.1.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8.1.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8.1"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8.1.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8.1"
      - "C:/Program Files/nodejs/gcc-ranlib-8.1.com"
      - "C:/Program Files/nodejs/gcc-ranlib-8.1.exe"
      - "C:/Program Files/nodejs/gcc-ranlib-8.1"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8.1.com"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8.1"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.1.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.1.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.1"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8.1.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8.1.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8.1"
      - "C:/Program Files/dotnet/gcc-ranlib-8.1.com"
      - "C:/Program Files/dotnet/gcc-ranlib-8.1.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-8.1"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.1.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.1.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.1"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8.1.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8.1.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8.1"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8.1.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8.1"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8.1.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8.1"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8.1.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8.1"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8.1.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8.1"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8.1.com"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8.1.exe"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8.1"
      - "D:/APP/mingw64/bin/gcc-ranlib-8.com"
      - "D:/APP/mingw64/bin/gcc-ranlib-8.exe"
      - "D:/APP/mingw64/bin/gcc-ranlib-8"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8.com"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8.exe"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8"
      - "D:/APP/Vmware/bin/gcc-ranlib-8.com"
      - "D:/APP/Vmware/bin/gcc-ranlib-8.exe"
      - "D:/APP/Vmware/bin/gcc-ranlib-8"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8"
      - "C:/Windows/System32/gcc-ranlib-8.com"
      - "C:/Windows/System32/gcc-ranlib-8.exe"
      - "C:/Windows/System32/gcc-ranlib-8"
      - "C:/Windows/gcc-ranlib-8.com"
      - "C:/Windows/gcc-ranlib-8.exe"
      - "C:/Windows/gcc-ranlib-8"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8"
      - "C:/Program Files/nodejs/gcc-ranlib-8.com"
      - "C:/Program Files/nodejs/gcc-ranlib-8.exe"
      - "C:/Program Files/nodejs/gcc-ranlib-8"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8.com"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8.exe"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8"
      - "C:/Program Files/dotnet/gcc-ranlib-8.com"
      - "C:/Program Files/dotnet/gcc-ranlib-8.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8.com"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8.exe"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8"
      - "D:/APP/mingw64/bin/gcc-ranlib8.com"
      - "D:/APP/mingw64/bin/gcc-ranlib8.exe"
      - "D:/APP/mingw64/bin/gcc-ranlib8"
      - "C:/Program Files/PowerShell/7/gcc-ranlib8.com"
      - "C:/Program Files/PowerShell/7/gcc-ranlib8.exe"
      - "C:/Program Files/PowerShell/7/gcc-ranlib8"
      - "D:/APP/Vmware/bin/gcc-ranlib8.com"
      - "D:/APP/Vmware/bin/gcc-ranlib8.exe"
      - "D:/APP/Vmware/bin/gcc-ranlib8"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib8.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib8.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib8"
      - "C:/Windows/System32/gcc-ranlib8.com"
      - "C:/Windows/System32/gcc-ranlib8.exe"
      - "C:/Windows/System32/gcc-ranlib8"
      - "C:/Windows/gcc-ranlib8.com"
      - "C:/Windows/gcc-ranlib8.exe"
      - "C:/Windows/gcc-ranlib8"
      - "C:/Windows/System32/wbem/gcc-ranlib8.com"
      - "C:/Windows/System32/wbem/gcc-ranlib8.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib8"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib8.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib8.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib8"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib8.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib8.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib8"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib8.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib8.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib8"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib8.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib8.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib8"
      - "C:/Program Files/nodejs/gcc-ranlib8.com"
      - "C:/Program Files/nodejs/gcc-ranlib8.exe"
      - "C:/Program Files/nodejs/gcc-ranlib8"
      - "D:/APP/MATLAB/bin/gcc-ranlib8.com"
      - "D:/APP/MATLAB/bin/gcc-ranlib8.exe"
      - "D:/APP/MATLAB/bin/gcc-ranlib8"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib8.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib8.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib8"
      - "C:/Program Files/Git/cmd/gcc-ranlib8.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib8.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib8"
      - "C:/Program Files/dotnet/gcc-ranlib8.com"
      - "C:/Program Files/dotnet/gcc-ranlib8.exe"
      - "C:/Program Files/dotnet/gcc-ranlib8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib8"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib8.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib8.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib8"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib8.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib8.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib8"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib8.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib8.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib8"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib8.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib8.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib8"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib8.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib8.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib8"
      - "D:/APP/ninja/ninja-win/gcc-ranlib8.com"
      - "D:/APP/ninja/ninja-win/gcc-ranlib8.exe"
      - "D:/APP/ninja/ninja-win/gcc-ranlib8"
      - "D:/APP/mingw64/bin/gcc-ranlib.com"
    found: "D:/APP/mingw64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:54 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:61 (_cmake_find_compiler)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_ASM_COMPILER"
    description: "ASM compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "D:/APP/mingw64/bin/gcc.exe"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
    searched_directories:
      - "D:/APP/mingw64/bin/gcc.exe.com"
    found: "D:/APP/mingw64/bin/gcc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1290 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "CMakeLists.txt:9 (enable_language)"
    message: |
      Checking whether the ASM compiler is GNU using "--version" matched "(GNU assembler)|(GCC)|(Free Software Foundation)":
      gcc.exe (x86_64-posix-seh-rev0, Built by MinGW-W64 project) 8.1.0
      Copyright (C) 2018 Free Software Foundation, Inc.
      This is free software; see the source for copying conditions.  There is NO
      warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
      
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:268 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_ASM_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-"
      - "gcc-ar-"
      - "gcc-ar"
      - "gcc-ar"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/gcc-ar-.com"
      - "D:/APP/mingw64/bin/gcc-ar-.exe"
      - "D:/APP/mingw64/bin/gcc-ar-"
      - "C:/Program Files/PowerShell/7/gcc-ar-.com"
      - "C:/Program Files/PowerShell/7/gcc-ar-.exe"
      - "C:/Program Files/PowerShell/7/gcc-ar-"
      - "D:/APP/Vmware/bin/gcc-ar-.com"
      - "D:/APP/Vmware/bin/gcc-ar-.exe"
      - "D:/APP/Vmware/bin/gcc-ar-"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-"
      - "C:/Windows/System32/gcc-ar-.com"
      - "C:/Windows/System32/gcc-ar-.exe"
      - "C:/Windows/System32/gcc-ar-"
      - "C:/Windows/gcc-ar-.com"
      - "C:/Windows/gcc-ar-.exe"
      - "C:/Windows/gcc-ar-"
      - "C:/Windows/System32/wbem/gcc-ar-.com"
      - "C:/Windows/System32/wbem/gcc-ar-.exe"
      - "C:/Windows/System32/wbem/gcc-ar-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-"
      - "C:/Windows/System32/OpenSSH/gcc-ar-.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-"
      - "C:/Program Files/nodejs/gcc-ar-.com"
      - "C:/Program Files/nodejs/gcc-ar-.exe"
      - "C:/Program Files/nodejs/gcc-ar-"
      - "D:/APP/MATLAB/bin/gcc-ar-.com"
      - "D:/APP/MATLAB/bin/gcc-ar-.exe"
      - "D:/APP/MATLAB/bin/gcc-ar-"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-"
      - "C:/Program Files/Git/cmd/gcc-ar-.com"
      - "C:/Program Files/Git/cmd/gcc-ar-.exe"
      - "C:/Program Files/Git/cmd/gcc-ar-"
      - "C:/Program Files/dotnet/gcc-ar-.com"
      - "C:/Program Files/dotnet/gcc-ar-.exe"
      - "C:/Program Files/dotnet/gcc-ar-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-"
      - "D:/APP/ninja/ninja-win/gcc-ar-.com"
      - "D:/APP/ninja/ninja-win/gcc-ar-.exe"
      - "D:/APP/ninja/ninja-win/gcc-ar-"
      - "D:/APP/mingw64/bin/gcc-ar-.com"
      - "D:/APP/mingw64/bin/gcc-ar-.exe"
      - "D:/APP/mingw64/bin/gcc-ar-"
      - "C:/Program Files/PowerShell/7/gcc-ar-.com"
      - "C:/Program Files/PowerShell/7/gcc-ar-.exe"
      - "C:/Program Files/PowerShell/7/gcc-ar-"
      - "D:/APP/Vmware/bin/gcc-ar-.com"
      - "D:/APP/Vmware/bin/gcc-ar-.exe"
      - "D:/APP/Vmware/bin/gcc-ar-"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-"
      - "C:/Windows/System32/gcc-ar-.com"
      - "C:/Windows/System32/gcc-ar-.exe"
      - "C:/Windows/System32/gcc-ar-"
      - "C:/Windows/gcc-ar-.com"
      - "C:/Windows/gcc-ar-.exe"
      - "C:/Windows/gcc-ar-"
      - "C:/Windows/System32/wbem/gcc-ar-.com"
      - "C:/Windows/System32/wbem/gcc-ar-.exe"
      - "C:/Windows/System32/wbem/gcc-ar-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-"
      - "C:/Windows/System32/OpenSSH/gcc-ar-.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-"
      - "C:/Program Files/nodejs/gcc-ar-.com"
      - "C:/Program Files/nodejs/gcc-ar-.exe"
      - "C:/Program Files/nodejs/gcc-ar-"
      - "D:/APP/MATLAB/bin/gcc-ar-.com"
      - "D:/APP/MATLAB/bin/gcc-ar-.exe"
      - "D:/APP/MATLAB/bin/gcc-ar-"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-"
      - "C:/Program Files/Git/cmd/gcc-ar-.com"
      - "C:/Program Files/Git/cmd/gcc-ar-.exe"
      - "C:/Program Files/Git/cmd/gcc-ar-"
      - "C:/Program Files/dotnet/gcc-ar-.com"
      - "C:/Program Files/dotnet/gcc-ar-.exe"
      - "C:/Program Files/dotnet/gcc-ar-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-"
      - "D:/APP/ninja/ninja-win/gcc-ar-.com"
      - "D:/APP/ninja/ninja-win/gcc-ar-.exe"
      - "D:/APP/ninja/ninja-win/gcc-ar-"
      - "D:/APP/mingw64/bin/gcc-ar.com"
    found: "D:/APP/mingw64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:268 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_ASM_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-"
      - "gcc-ranlib-"
      - "gcc-ranlib"
      - "gcc-ranlib"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/gcc-ranlib-.com"
      - "D:/APP/mingw64/bin/gcc-ranlib-.exe"
      - "D:/APP/mingw64/bin/gcc-ranlib-"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-.com"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-.exe"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-"
      - "D:/APP/Vmware/bin/gcc-ranlib-.com"
      - "D:/APP/Vmware/bin/gcc-ranlib-.exe"
      - "D:/APP/Vmware/bin/gcc-ranlib-"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-"
      - "C:/Windows/System32/gcc-ranlib-.com"
      - "C:/Windows/System32/gcc-ranlib-.exe"
      - "C:/Windows/System32/gcc-ranlib-"
      - "C:/Windows/gcc-ranlib-.com"
      - "C:/Windows/gcc-ranlib-.exe"
      - "C:/Windows/gcc-ranlib-"
      - "C:/Windows/System32/wbem/gcc-ranlib-.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-"
      - "C:/Program Files/nodejs/gcc-ranlib-.com"
      - "C:/Program Files/nodejs/gcc-ranlib-.exe"
      - "C:/Program Files/nodejs/gcc-ranlib-"
      - "D:/APP/MATLAB/bin/gcc-ranlib-.com"
      - "D:/APP/MATLAB/bin/gcc-ranlib-.exe"
      - "D:/APP/MATLAB/bin/gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-"
      - "C:/Program Files/Git/cmd/gcc-ranlib-.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib-.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib-"
      - "C:/Program Files/dotnet/gcc-ranlib-.com"
      - "C:/Program Files/dotnet/gcc-ranlib-.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-.com"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-.exe"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-"
      - "D:/APP/mingw64/bin/gcc-ranlib-.com"
      - "D:/APP/mingw64/bin/gcc-ranlib-.exe"
      - "D:/APP/mingw64/bin/gcc-ranlib-"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-.com"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-.exe"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-"
      - "D:/APP/Vmware/bin/gcc-ranlib-.com"
      - "D:/APP/Vmware/bin/gcc-ranlib-.exe"
      - "D:/APP/Vmware/bin/gcc-ranlib-"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-"
      - "C:/Windows/System32/gcc-ranlib-.com"
      - "C:/Windows/System32/gcc-ranlib-.exe"
      - "C:/Windows/System32/gcc-ranlib-"
      - "C:/Windows/gcc-ranlib-.com"
      - "C:/Windows/gcc-ranlib-.exe"
      - "C:/Windows/gcc-ranlib-"
      - "C:/Windows/System32/wbem/gcc-ranlib-.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-"
      - "C:/Program Files/nodejs/gcc-ranlib-.com"
      - "C:/Program Files/nodejs/gcc-ranlib-.exe"
      - "C:/Program Files/nodejs/gcc-ranlib-"
      - "D:/APP/MATLAB/bin/gcc-ranlib-.com"
      - "D:/APP/MATLAB/bin/gcc-ranlib-.exe"
      - "D:/APP/MATLAB/bin/gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-"
      - "C:/Program Files/Git/cmd/gcc-ranlib-.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib-.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib-"
      - "C:/Program Files/dotnet/gcc-ranlib-.com"
      - "C:/Program Files/dotnet/gcc-ranlib-.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-.com"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-.exe"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-"
      - "D:/APP/mingw64/bin/gcc-ranlib.com"
    found: "D:/APP/mingw64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:54 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:69 (_cmake_find_compiler)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER"
    description: "CXX compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "c++"
      - "g++"
      - "cl"
      - "bcc"
      - "icx"
      - "clang++"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
    searched_directories:
      - "D:/APP/mingw64/bin/c++.com"
    found: "D:/APP/mingw64/bin/c++.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/"
    found: "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:9 (enable_language)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: D:/APP/mingw64/bin/c++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/4.1.0-rc1/CompilerIdCXX/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-8.1"
      - "gcc-ar-8"
      - "gcc-ar8"
      - "gcc-ar"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/gcc-ar-8.1.com"
      - "D:/APP/mingw64/bin/gcc-ar-8.1.exe"
      - "D:/APP/mingw64/bin/gcc-ar-8.1"
      - "C:/Program Files/PowerShell/7/gcc-ar-8.1.com"
      - "C:/Program Files/PowerShell/7/gcc-ar-8.1.exe"
      - "C:/Program Files/PowerShell/7/gcc-ar-8.1"
      - "D:/APP/Vmware/bin/gcc-ar-8.1.com"
      - "D:/APP/Vmware/bin/gcc-ar-8.1.exe"
      - "D:/APP/Vmware/bin/gcc-ar-8.1"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8.1.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8.1.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8.1"
      - "C:/Windows/System32/gcc-ar-8.1.com"
      - "C:/Windows/System32/gcc-ar-8.1.exe"
      - "C:/Windows/System32/gcc-ar-8.1"
      - "C:/Windows/gcc-ar-8.1.com"
      - "C:/Windows/gcc-ar-8.1.exe"
      - "C:/Windows/gcc-ar-8.1"
      - "C:/Windows/System32/wbem/gcc-ar-8.1.com"
      - "C:/Windows/System32/wbem/gcc-ar-8.1.exe"
      - "C:/Windows/System32/wbem/gcc-ar-8.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.1"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8.1.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8.1.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8.1"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8.1.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8.1.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8.1"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8.1.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8.1.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8.1"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8.1.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8.1.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8.1"
      - "C:/Program Files/nodejs/gcc-ar-8.1.com"
      - "C:/Program Files/nodejs/gcc-ar-8.1.exe"
      - "C:/Program Files/nodejs/gcc-ar-8.1"
      - "D:/APP/MATLAB/bin/gcc-ar-8.1.com"
      - "D:/APP/MATLAB/bin/gcc-ar-8.1.exe"
      - "D:/APP/MATLAB/bin/gcc-ar-8.1"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.1.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.1.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.1"
      - "C:/Program Files/Git/cmd/gcc-ar-8.1.com"
      - "C:/Program Files/Git/cmd/gcc-ar-8.1.exe"
      - "C:/Program Files/Git/cmd/gcc-ar-8.1"
      - "C:/Program Files/dotnet/gcc-ar-8.1.com"
      - "C:/Program Files/dotnet/gcc-ar-8.1.exe"
      - "C:/Program Files/dotnet/gcc-ar-8.1"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.1.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.1.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.1"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8.1.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8.1.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8.1"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8.1.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8.1.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8.1"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8.1.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8.1.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8.1"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8.1.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8.1.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8.1"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8.1.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8.1.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8.1"
      - "D:/APP/ninja/ninja-win/gcc-ar-8.1.com"
      - "D:/APP/ninja/ninja-win/gcc-ar-8.1.exe"
      - "D:/APP/ninja/ninja-win/gcc-ar-8.1"
      - "D:/APP/mingw64/bin/gcc-ar-8.com"
      - "D:/APP/mingw64/bin/gcc-ar-8.exe"
      - "D:/APP/mingw64/bin/gcc-ar-8"
      - "C:/Program Files/PowerShell/7/gcc-ar-8.com"
      - "C:/Program Files/PowerShell/7/gcc-ar-8.exe"
      - "C:/Program Files/PowerShell/7/gcc-ar-8"
      - "D:/APP/Vmware/bin/gcc-ar-8.com"
      - "D:/APP/Vmware/bin/gcc-ar-8.exe"
      - "D:/APP/Vmware/bin/gcc-ar-8"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar-8"
      - "C:/Windows/System32/gcc-ar-8.com"
      - "C:/Windows/System32/gcc-ar-8.exe"
      - "C:/Windows/System32/gcc-ar-8"
      - "C:/Windows/gcc-ar-8.com"
      - "C:/Windows/gcc-ar-8.exe"
      - "C:/Windows/gcc-ar-8"
      - "C:/Windows/System32/wbem/gcc-ar-8.com"
      - "C:/Windows/System32/wbem/gcc-ar-8.exe"
      - "C:/Windows/System32/wbem/gcc-ar-8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-8"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-8"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-8"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-8"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar-8"
      - "C:/Program Files/nodejs/gcc-ar-8.com"
      - "C:/Program Files/nodejs/gcc-ar-8.exe"
      - "C:/Program Files/nodejs/gcc-ar-8"
      - "D:/APP/MATLAB/bin/gcc-ar-8.com"
      - "D:/APP/MATLAB/bin/gcc-ar-8.exe"
      - "D:/APP/MATLAB/bin/gcc-ar-8"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar-8"
      - "C:/Program Files/Git/cmd/gcc-ar-8.com"
      - "C:/Program Files/Git/cmd/gcc-ar-8.exe"
      - "C:/Program Files/Git/cmd/gcc-ar-8"
      - "C:/Program Files/dotnet/gcc-ar-8.com"
      - "C:/Program Files/dotnet/gcc-ar-8.exe"
      - "C:/Program Files/dotnet/gcc-ar-8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-8"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-8"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar-8"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar-8"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar-8"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar-8"
      - "D:/APP/ninja/ninja-win/gcc-ar-8.com"
      - "D:/APP/ninja/ninja-win/gcc-ar-8.exe"
      - "D:/APP/ninja/ninja-win/gcc-ar-8"
      - "D:/APP/mingw64/bin/gcc-ar8.com"
      - "D:/APP/mingw64/bin/gcc-ar8.exe"
      - "D:/APP/mingw64/bin/gcc-ar8"
      - "C:/Program Files/PowerShell/7/gcc-ar8.com"
      - "C:/Program Files/PowerShell/7/gcc-ar8.exe"
      - "C:/Program Files/PowerShell/7/gcc-ar8"
      - "D:/APP/Vmware/bin/gcc-ar8.com"
      - "D:/APP/Vmware/bin/gcc-ar8.exe"
      - "D:/APP/Vmware/bin/gcc-ar8"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar8.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar8.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ar8"
      - "C:/Windows/System32/gcc-ar8.com"
      - "C:/Windows/System32/gcc-ar8.exe"
      - "C:/Windows/System32/gcc-ar8"
      - "C:/Windows/gcc-ar8.com"
      - "C:/Windows/gcc-ar8.exe"
      - "C:/Windows/gcc-ar8"
      - "C:/Windows/System32/wbem/gcc-ar8.com"
      - "C:/Windows/System32/wbem/gcc-ar8.exe"
      - "C:/Windows/System32/wbem/gcc-ar8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar8"
      - "C:/Windows/System32/OpenSSH/gcc-ar8.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar8.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar8"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar8.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar8.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar8"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar8.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar8.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar8"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar8.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar8.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ar8"
      - "C:/Program Files/nodejs/gcc-ar8.com"
      - "C:/Program Files/nodejs/gcc-ar8.exe"
      - "C:/Program Files/nodejs/gcc-ar8"
      - "D:/APP/MATLAB/bin/gcc-ar8.com"
      - "D:/APP/MATLAB/bin/gcc-ar8.exe"
      - "D:/APP/MATLAB/bin/gcc-ar8"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar8.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar8.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ar8"
      - "C:/Program Files/Git/cmd/gcc-ar8.com"
      - "C:/Program Files/Git/cmd/gcc-ar8.exe"
      - "C:/Program Files/Git/cmd/gcc-ar8"
      - "C:/Program Files/dotnet/gcc-ar8.com"
      - "C:/Program Files/dotnet/gcc-ar8.exe"
      - "C:/Program Files/dotnet/gcc-ar8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar8"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar8.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar8.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar8"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar8.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar8.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ar8"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar8.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar8.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ar8"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar8.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar8.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ar8"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar8.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar8.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ar8"
      - "D:/APP/ninja/ninja-win/gcc-ar8.com"
      - "D:/APP/ninja/ninja-win/gcc-ar8.exe"
      - "D:/APP/ninja/ninja-win/gcc-ar8"
      - "D:/APP/mingw64/bin/gcc-ar.com"
    found: "D:/APP/mingw64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-8.1"
      - "gcc-ranlib-8"
      - "gcc-ranlib8"
      - "gcc-ranlib"
    candidate_directories:
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
    searched_directories:
      - "D:/APP/mingw64/bin/gcc-ranlib-8.1.com"
      - "D:/APP/mingw64/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/mingw64/bin/gcc-ranlib-8.1"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8.1.com"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8.1.exe"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8.1"
      - "D:/APP/Vmware/bin/gcc-ranlib-8.1.com"
      - "D:/APP/Vmware/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/Vmware/bin/gcc-ranlib-8.1"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8.1.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8.1.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8.1"
      - "C:/Windows/System32/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/gcc-ranlib-8.1"
      - "C:/Windows/gcc-ranlib-8.1.com"
      - "C:/Windows/gcc-ranlib-8.1.exe"
      - "C:/Windows/gcc-ranlib-8.1"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.1"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8.1.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8.1.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8.1"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8.1.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8.1.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8.1"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8.1.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8.1.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8.1"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8.1.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8.1"
      - "C:/Program Files/nodejs/gcc-ranlib-8.1.com"
      - "C:/Program Files/nodejs/gcc-ranlib-8.1.exe"
      - "C:/Program Files/nodejs/gcc-ranlib-8.1"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8.1.com"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8.1"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.1.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.1.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.1"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8.1.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8.1.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8.1"
      - "C:/Program Files/dotnet/gcc-ranlib-8.1.com"
      - "C:/Program Files/dotnet/gcc-ranlib-8.1.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-8.1"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.1.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.1.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.1"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8.1.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8.1.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8.1"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8.1.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8.1"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8.1.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8.1"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8.1.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8.1"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8.1.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8.1.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8.1"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8.1.com"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8.1.exe"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8.1"
      - "D:/APP/mingw64/bin/gcc-ranlib-8.com"
      - "D:/APP/mingw64/bin/gcc-ranlib-8.exe"
      - "D:/APP/mingw64/bin/gcc-ranlib-8"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8.com"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8.exe"
      - "C:/Program Files/PowerShell/7/gcc-ranlib-8"
      - "D:/APP/Vmware/bin/gcc-ranlib-8.com"
      - "D:/APP/Vmware/bin/gcc-ranlib-8.exe"
      - "D:/APP/Vmware/bin/gcc-ranlib-8"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib-8"
      - "C:/Windows/System32/gcc-ranlib-8.com"
      - "C:/Windows/System32/gcc-ranlib-8.exe"
      - "C:/Windows/System32/gcc-ranlib-8"
      - "C:/Windows/gcc-ranlib-8.com"
      - "C:/Windows/gcc-ranlib-8.exe"
      - "C:/Windows/gcc-ranlib-8"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-8.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-8"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-8"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-8"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-8"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib-8"
      - "C:/Program Files/nodejs/gcc-ranlib-8.com"
      - "C:/Program Files/nodejs/gcc-ranlib-8.exe"
      - "C:/Program Files/nodejs/gcc-ranlib-8"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8.com"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8.exe"
      - "D:/APP/MATLAB/bin/gcc-ranlib-8"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib-8"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib-8"
      - "C:/Program Files/dotnet/gcc-ranlib-8.com"
      - "C:/Program Files/dotnet/gcc-ranlib-8.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-8"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-8"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib-8"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib-8"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib-8"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib-8"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8.com"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8.exe"
      - "D:/APP/ninja/ninja-win/gcc-ranlib-8"
      - "D:/APP/mingw64/bin/gcc-ranlib8.com"
      - "D:/APP/mingw64/bin/gcc-ranlib8.exe"
      - "D:/APP/mingw64/bin/gcc-ranlib8"
      - "C:/Program Files/PowerShell/7/gcc-ranlib8.com"
      - "C:/Program Files/PowerShell/7/gcc-ranlib8.exe"
      - "C:/Program Files/PowerShell/7/gcc-ranlib8"
      - "D:/APP/Vmware/bin/gcc-ranlib8.com"
      - "D:/APP/Vmware/bin/gcc-ranlib8.exe"
      - "D:/APP/Vmware/bin/gcc-ranlib8"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib8.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib8.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/gcc-ranlib8"
      - "C:/Windows/System32/gcc-ranlib8.com"
      - "C:/Windows/System32/gcc-ranlib8.exe"
      - "C:/Windows/System32/gcc-ranlib8"
      - "C:/Windows/gcc-ranlib8.com"
      - "C:/Windows/gcc-ranlib8.exe"
      - "C:/Windows/gcc-ranlib8"
      - "C:/Windows/System32/wbem/gcc-ranlib8.com"
      - "C:/Windows/System32/wbem/gcc-ranlib8.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib8"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib8.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib8.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib8"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib8.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib8.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib8"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib8.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib8.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib8"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib8.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib8.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib8"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib8.com"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib8.exe"
      - "D:/APP/Microsoft VS Code/bin/gcc-ranlib8"
      - "C:/Program Files/nodejs/gcc-ranlib8.com"
      - "C:/Program Files/nodejs/gcc-ranlib8.exe"
      - "C:/Program Files/nodejs/gcc-ranlib8"
      - "D:/APP/MATLAB/bin/gcc-ranlib8.com"
      - "D:/APP/MATLAB/bin/gcc-ranlib8.exe"
      - "D:/APP/MATLAB/bin/gcc-ranlib8"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib8.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib8.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/gcc-ranlib8"
      - "C:/Program Files/Git/cmd/gcc-ranlib8.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib8.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib8"
      - "C:/Program Files/dotnet/gcc-ranlib8.com"
      - "C:/Program Files/dotnet/gcc-ranlib8.exe"
      - "C:/Program Files/dotnet/gcc-ranlib8"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib8.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib8.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib8"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib8.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib8.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib8"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib8.com"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib8.exe"
      - "D:/APP/clion/CLion_2025_1/bin/gcc-ranlib8"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib8.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib8.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/gcc-ranlib8"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib8.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib8.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/gcc-ranlib8"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib8.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib8.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/gcc-ranlib8"
      - "D:/APP/ninja/ninja-win/gcc-ranlib8.com"
      - "D:/APP/ninja/ninja-win/gcc-ranlib8.exe"
      - "D:/APP/ninja/ninja-win/gcc-ranlib8"
      - "D:/APP/mingw64/bin/gcc-ranlib.com"
    found: "D:/APP/mingw64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake:167 (enable_language)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-C.cmake:2 (__windows_compiler_gnu)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:9 (enable_language)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "windres"
      - "windres"
    candidate_directories:
      - "C:/Program Files/PowerShell/7/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/sbin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/"
      - "C:/Program Files (x86)/write_cmake_test/bin/"
      - "C:/Program Files (x86)/write_cmake_test/sbin/"
      - "C:/Program Files (x86)/write_cmake_test/"
    searched_directories:
      - "C:/Program Files/PowerShell/7/windres.com"
      - "C:/Program Files/PowerShell/7/windres.exe"
      - "C:/Program Files/PowerShell/7/windres"
      - "D:/APP/Vmware/bin/windres.com"
      - "D:/APP/Vmware/bin/windres.exe"
      - "D:/APP/Vmware/bin/windres"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/windres.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/windres.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/windres"
      - "C:/Windows/System32/windres.com"
      - "C:/Windows/System32/windres.exe"
      - "C:/Windows/System32/windres"
      - "C:/Windows/windres.com"
      - "C:/Windows/windres.exe"
      - "C:/Windows/windres"
      - "C:/Windows/System32/wbem/windres.com"
      - "C:/Windows/System32/wbem/windres.exe"
      - "C:/Windows/System32/wbem/windres"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/windres.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/windres.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/windres"
      - "C:/Windows/System32/OpenSSH/windres.com"
      - "C:/Windows/System32/OpenSSH/windres.exe"
      - "C:/Windows/System32/OpenSSH/windres"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/windres.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/windres.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/windres"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/windres.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/windres.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/windres"
      - "D:/APP/Microsoft VS Code/bin/windres.com"
      - "D:/APP/Microsoft VS Code/bin/windres.exe"
      - "D:/APP/Microsoft VS Code/bin/windres"
      - "D:/APP/mingw64/bin/windres.com"
    found: "D:/APP/mingw64/bin/windres.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files\\PowerShell\\7"
        - "D:\\APP\\Vmware\\bin\\"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "D:\\APP\\MATLAB\\bin"
        - "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/write_cmake_test"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64"
        - "C:/Program Files (x86)/write_cmake_test"
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:9 (enable_language)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-bqrxlx"
      binary: "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-bqrxlx"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-bqrxlx'
        
        Run Build Command(s): D:/APP/ninja/ninja-win/ninja.exe -v cmTC_515c2
        [1/2] D:\\APP\\mingw64\\bin\\gcc.exe   -std=gnu11   -v -o CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj -c D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=D:\\APP\\mingw64\\bin\\gcc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib '
        Thread model: posix
        gcc version 8.1.0 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) 
        COLLECT_GCC_OPTIONS='-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona'
         D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/cc1.exe -quiet -v -iprefix D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/ -D_REENTRANT D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=core2 -march=nocona -auxbase-strip CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj -std=gnu11 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccROUdAR.s
        GNU C11 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 8.1.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include"
        ignoring nonexistent directory "C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../include"
        ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed"
        ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        GNU C11 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 8.1.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 985ce7ae6dd3a696cd146ca9896b0035
        COLLECT_GCC_OPTIONS='-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona'
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccROUdAR.s
        GNU assembler version 2.30 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.30
        COMPILER_PATH=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/;D:/APP/mingw64/bin/../libexec/gcc/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/;D:/APP/mingw64/bin/../lib/gcc/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../
        COLLECT_GCC_OPTIONS='-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona'
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && D:\\APP\\mingw64\\bin\\gcc.exe  -v -Wl,-v CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj -o cmTC_515c2.exe -Wl,--out-implib,libcmTC_515c2.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        Using built-in specs.
        COLLECT_GCC=D:\\APP\\mingw64\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib '
        Thread model: posix
        gcc version 8.1.0 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) 
        COMPILER_PATH=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/;D:/APP/mingw64/bin/../libexec/gcc/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/;D:/APP/mingw64/bin/../lib/gcc/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_515c2.exe' '-mtune=core2' '-march=nocona'
         D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cctAltQS.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_515c2.exe D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LD:/APP/mingw64/bin/../lib/gcc -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. -v CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_515c2.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o
        collect2 version 8.1.0
        D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cctAltQS.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_515c2.exe D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LD:/APP/mingw64/bin/../lib/gcc -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. -v CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_515c2.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o
        GNU ld (GNU Binutils) 2.30
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_515c2.exe' '-mtune=core2' '-march=nocona'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:9 (enable_language)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include]
          add: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
          add: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include]
        collapse include dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
        collapse include dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include] ==> [D:/APP/mingw64/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include;D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed;D:/APP/mingw64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:9 (enable_language)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-bqrxlx']
        ignore line: []
        ignore line: [Run Build Command(s): D:/APP/ninja/ninja-win/ninja.exe -v cmTC_515c2]
        ignore line: [[1/2] D:\\APP\\mingw64\\bin\\gcc.exe   -std=gnu11   -v -o CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj -c D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\APP\\mingw64\\bin\\gcc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib ']
        ignore line: [Thread model: posix]
        ignore line: [gcc version 8.1.0 (x86_64-posix-seh-rev0  Built by MinGW-W64 project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona']
        ignore line: [ D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/cc1.exe -quiet -v -iprefix D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/ -D_REENTRANT D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=core2 -march=nocona -auxbase-strip CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj -std=gnu11 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccROUdAR.s]
        ignore line: [GNU C11 (x86_64-posix-seh-rev0  Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 8.1.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include"]
        ignore line: [ignoring nonexistent directory "C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../include"]
        ignore line: [ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include]
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [GNU C11 (x86_64-posix-seh-rev0  Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 8.1.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 985ce7ae6dd3a696cd146ca9896b0035]
        ignore line: [COLLECT_GCC_OPTIONS='-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona']
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccROUdAR.s]
        ignore line: [GNU assembler version 2.30 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.30]
        ignore line: [COMPILER_PATH=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/]
        ignore line: [D:/APP/mingw64/bin/../libexec/gcc/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona']
        ignore line: [[2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && D:\\APP\\mingw64\\bin\\gcc.exe  -v -Wl -v CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj -o cmTC_515c2.exe -Wl --out-implib libcmTC_515c2.dll.a -Wl --major-image-version 0 --minor-image-version 0   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\APP\\mingw64\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib ']
        ignore line: [Thread model: posix]
        ignore line: [gcc version 8.1.0 (x86_64-posix-seh-rev0  Built by MinGW-W64 project) ]
        ignore line: [COMPILER_PATH=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/]
        ignore line: [D:/APP/mingw64/bin/../libexec/gcc/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_515c2.exe' '-mtune=core2' '-march=nocona']
        link line: [ D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cctAltQS.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_515c2.exe D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LD:/APP/mingw64/bin/../lib/gcc -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. -v CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_515c2.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
          arg [D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll] ==> ignore
          arg [-plugin-opt=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cctAltQS.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-liconv] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [--sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_515c2.exe] ==> ignore
          arg [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o] ==> obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o]
          arg [-LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0]
          arg [-LD:/APP/mingw64/bin/../lib/gcc] ==> dir [D:/APP/mingw64/bin/../lib/gcc]
          arg [-LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib]
          arg [-LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_515c2.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o] ==> obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
        ignore line: [collect2 version 8.1.0]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cctAltQS.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_515c2.exe D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LD:/APP/mingw64/bin/../lib/gcc -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. -v CMakeFiles/cmTC_515c2.dir/CMakeCCompilerABI.c.obj --out-implib libcmTC_515c2.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
        linker tool for 'C': D:/APP/mingw64/x86_64-w64-mingw32/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [D:/APP/mingw64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o]
        collapse obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc] ==> [D:/APP/mingw64/lib/gcc]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [D:/APP/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> [D:/APP/mingw64/lib]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> [D:/APP/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> [D:/APP/mingw64/lib]
        implicit libs: [mingw32;gcc;moldname;mingwex;pthread;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc;moldname;mingwex]
        implicit objs: [D:/APP/mingw64/x86_64-w64-mingw32/lib/crt2.o;D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o;D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
        implicit dirs: [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0;D:/APP/mingw64/lib/gcc;D:/APP/mingw64/x86_64-w64-mingw32/lib;D:/APP/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:9 (enable_language)"
    message: |
      Running the C compiler's linker: "D:/APP/mingw64/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.30
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:9 (enable_language)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-uziorv"
      binary: "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-uziorv"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-uziorv'
        
        Run Build Command(s): D:/APP/ninja/ninja-win/ninja.exe -v cmTC_d0441
        [1/2] D:\\APP\\mingw64\\bin\\c++.exe   -v -o CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj -c D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=D:\\APP\\mingw64\\bin\\c++.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib '
        Thread model: posix
        gcc version 8.1.0 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona'
         D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/cc1plus.exe -quiet -v -iprefix D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/ -D_REENTRANT D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=core2 -march=nocona -auxbase-strip CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc5NU6bc.s
        GNU C++14 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 8.1.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++"
        ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32"
        ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward"
        ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include"
        ignoring nonexistent directory "C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../include"
        ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed"
        ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        GNU C++14 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 8.1.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 82f0c9785fd37a38ba7b7f8357369a82
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona'
         D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc5NU6bc.s
        GNU assembler version 2.30 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.30
        COMPILER_PATH=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/;D:/APP/mingw64/bin/../libexec/gcc/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/;D:/APP/mingw64/bin/../lib/gcc/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona'
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && D:\\APP\\mingw64\\bin\\c++.exe  -v -Wl,-v CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_d0441.exe -Wl,--out-implib,libcmTC_d0441.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        Using built-in specs.
        COLLECT_GCC=D:\\APP\\mingw64\\bin\\c++.exe
        COLLECT_LTO_WRAPPER=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib '
        Thread model: posix
        gcc version 8.1.0 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) 
        COMPILER_PATH=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/;D:/APP/mingw64/bin/../libexec/gcc/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/;D:/APP/mingw64/bin/../lib/gcc/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/;D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d0441.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona'
         D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccVaIP9h.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_d0441.exe D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LD:/APP/mingw64/bin/../lib/gcc -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. -v CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_d0441.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o
        collect2 version 8.1.0
        D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccVaIP9h.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_d0441.exe D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LD:/APP/mingw64/bin/../lib/gcc -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. -v CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_d0441.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o
        GNU ld (GNU Binutils) 2.30
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d0441.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:9 (enable_language)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++]
          add: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32]
          add: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward]
          add: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include]
          add: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
          add: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++]
        collapse include dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32]
        collapse include dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward]
        collapse include dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include]
        collapse include dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
        collapse include dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include] ==> [D:/APP/mingw64/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++;D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32;D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward;D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include;D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed;D:/APP/mingw64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:9 (enable_language)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-uziorv']
        ignore line: []
        ignore line: [Run Build Command(s): D:/APP/ninja/ninja-win/ninja.exe -v cmTC_d0441]
        ignore line: [[1/2] D:\\APP\\mingw64\\bin\\c++.exe   -v -o CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj -c D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\APP\\mingw64\\bin\\c++.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib ']
        ignore line: [Thread model: posix]
        ignore line: [gcc version 8.1.0 (x86_64-posix-seh-rev0  Built by MinGW-W64 project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona']
        ignore line: [ D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/cc1plus.exe -quiet -v -iprefix D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/ -D_REENTRANT D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=core2 -march=nocona -auxbase-strip CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc5NU6bc.s]
        ignore line: [GNU C++14 (x86_64-posix-seh-rev0  Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 8.1.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++"]
        ignore line: [ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward"]
        ignore line: [ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include"]
        ignore line: [ignoring nonexistent directory "C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../include"]
        ignore line: [ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/APP/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++]
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32]
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward]
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include]
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed]
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++14 (x86_64-posix-seh-rev0  Built by MinGW-W64 project) version 8.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 8.1.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 82f0c9785fd37a38ba7b7f8357369a82]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona']
        ignore line: [ D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc5NU6bc.s]
        ignore line: [GNU assembler version 2.30 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.30]
        ignore line: [COMPILER_PATH=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/]
        ignore line: [D:/APP/mingw64/bin/../libexec/gcc/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona']
        ignore line: [[2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && D:\\APP\\mingw64\\bin\\c++.exe  -v -Wl -v CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_d0441.exe -Wl --out-implib libcmTC_d0441.dll.a -Wl --major-image-version 0 --minor-image-version 0   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\APP\\mingw64\\bin\\c++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib ']
        ignore line: [Thread model: posix]
        ignore line: [gcc version 8.1.0 (x86_64-posix-seh-rev0  Built by MinGW-W64 project) ]
        ignore line: [COMPILER_PATH=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/]
        ignore line: [D:/APP/mingw64/bin/../libexec/gcc/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d0441.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona']
        link line: [ D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccVaIP9h.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_d0441.exe D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LD:/APP/mingw64/bin/../lib/gcc -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. -v CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_d0441.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
          arg [D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll] ==> ignore
          arg [-plugin-opt=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccVaIP9h.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-liconv] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [--sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_d0441.exe] ==> ignore
          arg [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o] ==> obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o]
          arg [-LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0]
          arg [-LD:/APP/mingw64/bin/../lib/gcc] ==> dir [D:/APP/mingw64/bin/../lib/gcc]
          arg [-LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib]
          arg [-LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_d0441.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o] ==> obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
        ignore line: [collect2 version 8.1.0]
        ignore line: [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=D:/APP/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccVaIP9h.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_d0441.exe D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LD:/APP/mingw64/bin/../lib/gcc -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LD:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. -v CMakeFiles/cmTC_d0441.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_d0441.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
        linker tool for 'CXX': D:/APP/mingw64/x86_64-w64-mingw32/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [D:/APP/mingw64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o]
        collapse obj [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc] ==> [D:/APP/mingw64/lib/gcc]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [D:/APP/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> [D:/APP/mingw64/lib]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> [D:/APP/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/APP/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> [D:/APP/mingw64/lib]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;moldname;mingwex;pthread;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc_s;gcc;moldname;mingwex]
        implicit objs: [D:/APP/mingw64/x86_64-w64-mingw32/lib/crt2.o;D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o;D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
        implicit dirs: [D:/APP/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0;D:/APP/mingw64/lib/gcc;D:/APP/mingw64/x86_64-w64-mingw32/lib;D:/APP/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:9 (enable_language)"
    message: |
      Running the CXX compiler's linker: "D:/APP/mingw64/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.30
...

# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.1

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: write_cmake_test
# Configurations: Debug
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__write_cmake_test_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}arm-none-eabi-gcc $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for linking ASM executable.

rule ASM_EXECUTABLE_LINKER__write_cmake_test_Debug
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && arm-none-eabi-gcc $FLAGS $LINK_FLAGS $in -o $TARGET_FILE -Wl,--out-implib,$TARGET_IMPLIB -Wl,--major-image-version,0,--minor-image-version,0 $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking ASM executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = D:\APP\cmake\cmake-4.1.0-rc1-windows-x86_64\cmake-4.1.0-rc1-windows-x86_64\bin\cmake.exe --regenerate-during-build -SD:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode -BD:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\build
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = D:\APP\ninja\ninja-win\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = D:\APP\ninja\ninja-win\ninja.exe -t targets
  description = All primary targets available:


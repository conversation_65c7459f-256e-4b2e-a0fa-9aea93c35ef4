# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.1

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: write_cmake_test
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/
# =============================================================================
# Object build statements for EXECUTABLE target write_cmake_test


#############################################
# Order-only phony target for write_cmake_test

build cmake_object_order_depends_target_write_cmake_test: phony || .

build CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj: ASM_COMPILER__write_cmake_test_unscanned_Debug D$:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/startup_stm32f401xc.s || cmake_object_order_depends_target_write_cmake_test
  CONFIG = Debug
  DEFINES = -DSTM32F401xC -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\write_cmake_test.dir\startup_stm32f401xc.s.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -Wall -Wextra -Wpedantic -O0 -g3 -fdata-sections -ffunction-sections -x assembler-with-cpp -MMD -MP -g
  INCLUDES = -ID:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/Core/App/Inc -ID:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/Core/User/Inc -ID:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/Core/Components/Inc
  OBJECT_DIR = CMakeFiles\write_cmake_test.dir
  OBJECT_FILE_DIR = CMakeFiles\write_cmake_test.dir
  TARGET_COMPILE_PDB = CMakeFiles\write_cmake_test.dir\
  TARGET_PDB = D:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\output\write_cmake_test.pdb


# =============================================================================
# Link build statements for EXECUTABLE target write_cmake_test


#############################################
# Link the executable D:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\output\write_cmake_test.elf

build D$:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test.elf: ASM_EXECUTABLE_LINKER__write_cmake_test_Debug CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj
  CONFIG = Debug
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -Wall -Wextra -Wpedantic -O0 -g3 -fdata-sections -ffunction-sections -x assembler-with-cpp -MMD -MP -g
  OBJECT_DIR = CMakeFiles\write_cmake_test.dir
  POST_BUILD = C:\Windows\system32\cmd.exe /C "cd /D D:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\build && arm-none-eabi-objcopy -O binary D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test.elf D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test.bin && arm-none-eabi-objcopy -O ihex D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test.elf D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test.hex && arm-none-eabi-objdump -d D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test.elf > D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test.s && echo "ninja 编译成功！""
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\write_cmake_test.dir\
  TARGET_FILE = D:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\output\write_cmake_test.elf
  TARGET_IMPLIB = libwrite_cmake_test.dll.a
  TARGET_PDB = D:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\output\write_cmake_test.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\build && D:\APP\cmake\cmake-4.1.0-rc1-windows-x86_64\cmake-4.1.0-rc1-windows-x86_64\bin\cmake-gui.exe -SD:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode -BD:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\build && D:\APP\cmake\cmake-4.1.0-rc1-windows-x86_64\cmake-4.1.0-rc1-windows-x86_64\bin\cmake.exe --regenerate-during-build -SD:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode -BD:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build write_cmake_test: phony D$:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test.elf

build write_cmake_test.elf: phony D$:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test.elf

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build

build all: phony D$:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test.elf

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja D$:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/cmake_install.cmake: RERUN_CMAKE | CMakeCache.txt CMakeFiles/4.1.0-rc1/CMakeASMCompiler.cmake CMakeFiles/4.1.0-rc1/CMakeCCompiler.cmake CMakeFiles/4.1.0-rc1/CMakeCXXCompiler.cmake CMakeFiles/4.1.0-rc1/CMakeRCCompiler.cmake CMakeFiles/4.1.0-rc1/CMakeSystem.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeASMCompiler.cmake.in D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeASMInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompiler.cmake.in D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerABI.c D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompiler.cmake.in D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCommonLanguageInclude.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCompilerIdDetection.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerSupport.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeGenericSystem.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeInitializeConfigs.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeLanguageInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeNinjaFindMake.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeParseImplicitLinkInfo.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeParseLibraryArchitecture.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeRCCompiler.cmake.in D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeRCInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeSystem.cmake.in D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeSystemSpecificInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeSystemSpecificInitialize.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestASMCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCompilerCommon.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestRCCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/CrayClang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Diab-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-ASM.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-C.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-CXX.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/LCC-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/OrangeC-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Renesas-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/TI-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/TIClang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Tasking-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeASMLinkerInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeCLinkerInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeCXXLinkerInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeCommonLinkerInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeInspectASMLinker.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeInspectCLinker.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeInspectCXXLinker.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/FeatureTesting.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Linker/GNU-C.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Linker/GNU-CXX.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Linker/GNU.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/GNU.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/Windows-ASM.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-ASM.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-C.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-CXX.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-Determine-CXX.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-ASM.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-C-ABI.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-C.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX-ABI.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-Initialize.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-windres.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/WindowsPaths.cmake D$:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/4.1.0-rc1/CMakeASMCompiler.cmake CMakeFiles/4.1.0-rc1/CMakeCCompiler.cmake CMakeFiles/4.1.0-rc1/CMakeCXXCompiler.cmake CMakeFiles/4.1.0-rc1/CMakeRCCompiler.cmake CMakeFiles/4.1.0-rc1/CMakeSystem.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeASMCompiler.cmake.in D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeASMInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompiler.cmake.in D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerABI.c D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompiler.cmake.in D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCommonLanguageInclude.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCompilerIdDetection.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerSupport.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeGenericSystem.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeInitializeConfigs.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeLanguageInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeNinjaFindMake.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeParseImplicitLinkInfo.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeParseLibraryArchitecture.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeRCCompiler.cmake.in D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeRCInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeSystem.cmake.in D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeSystemSpecificInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeSystemSpecificInitialize.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestASMCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCompilerCommon.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestRCCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/CrayClang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Diab-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-ASM.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-C.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-CXX.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/LCC-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/OrangeC-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Renesas-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/TI-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/TIClang-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Tasking-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeASMLinkerInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeCLinkerInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeCXXLinkerInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeCommonLinkerInformation.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeInspectASMLinker.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeInspectCLinker.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/CMakeInspectCXXLinker.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Internal/FeatureTesting.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Linker/GNU-C.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Linker/GNU-CXX.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Linker/GNU.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/GNU.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/Windows-ASM.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-ASM.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-C.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-CXX.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-Determine-CXX.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-ASM.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-C-ABI.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-C.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX-ABI.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-Initialize.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows-windres.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/Windows.cmake D$:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Platform/WindowsPaths.cmake D$:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all

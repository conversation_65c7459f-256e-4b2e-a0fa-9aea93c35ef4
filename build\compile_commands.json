[{"directory": "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build", "command": "arm-none-eabi-gcc -DSTM32F401xC -DUSE_HAL_DRIVER -ID:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/Core/App/Inc -ID:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/Core/User/Inc -ID:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/Core/Components/Inc  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -Wall -Wextra -Wpedantic -O0 -g3 -fdata-sections -ffunction-sections -x assembler-with-cpp -MMD -MP -g -o CMakeFiles\\write_cmake_test.dir\\startup_stm32f401xc.s.obj -c D:\\ELECT\\CODE\\vscode\\ST\\stm32f401ccu6\\register_test\\register_mode\\startup_stm32f401xc.s", "file": "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/startup_stm32f401xc.s", "output": "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj"}]
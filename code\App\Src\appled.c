
// STM32F401CCU6寄存器地址定义
#define RCC_BASE        0x40023800
#define RCC_AHB1ENR     (*(volatile uint32_t *)(RCC_BASE + 0x30)) // AHB1时钟使能寄存器

#define GPIOA_BASE      0x40020000
#define GPIOA_MODER     (*(volatile uint32_t *)(GPIOA_BASE + 0x00)) // 模式寄存器
#define GPIOA_OTYPER    (*(volatile uint32_t *)(GPIOA_BASE + 0x04)) // 输出类型寄存器
#define GPIOA_OSPEEDR   (*(volatile uint32_t *)(GPIOA_BASE + 0x08)) // 输出速度寄存器
#define GPIOA_PUPDR     (*(volatile uint32_t *)(GPIOA_BASE + 0x0C)) // 上拉/下拉寄存器
#define GPIOA_BSRR      (*(volatile uint32_t *)(GPIOA_BASE + 0x18)) // 位设置/复位寄存器

// 初始化GPIOA的PA0, PA2, PA3为输出模式
void GPIOA_Init(void) {
    // 1. 使能GPIOA时钟
    RCC_AHB1ENR |= (1 << 0);  // 开启GPIOA时钟

    // 2. 配置PA0, PA2, PA3为通用推挽输出
    GPIOA_MODER &= ~(0xFU << (0 * 2));  // 清除PA0的模式位
    GPIOA_MODER |= (0x1 << (0 * 2));   // 设置PA0为输出模式 (01)
    
    GPIOA_MODER &= ~(0xFU << (2 * 2));  // 清除PA2的模式位
    GPIOA_MODER |= (0x1 << (2 * 2));   // 设置PA2为输出模式 (01)
    
    GPIOA_MODER &= ~(0xFU << (3 * 2));  // 清除PA3的模式位
    GPIOA_MODER |= (0x1 << (3 * 2));   // 设置PA3为输出模式 (01)

    // 3. 配置输出类型为推挽
    GPIOA_OTYPER &= ~(1 << 0);  // PA0推挽输出
    GPIOA_OTYPER &= ~(1 << 2);  // PA2推挽输出
    GPIOA_OTYPER &= ~(1 << 3);  // PA3推挽输出

    // 4. 配置输出速度（中等速度）
    GPIOA_OSPEEDR &= ~(0xFU << (0 * 2));  // 清除PA0速度位
    GPIOA_OSPEEDR |= (0x1 << (0 * 2));    // PA0中等速度 (01)
    
    GPIOA_OSPEEDR &= ~(0xFU << (2 * 2));  // 清除PA2速度位
    GPIOA_OSPEEDR |= (0x1 << (2 * 2));    // PA2中等速度 (01)
    
    GPIOA_OSPEEDR &= ~(0xFU << (3 * 2));  // 清除PA3速度位
    GPIOA_OSPEEDR |= (0x1 << (3 * 2));    // PA3中等速度 (01)

    // 5. 配置无上拉/下拉
    GPIOA_PUPDR &= ~(0xFU << (0 * 2));  // PA0无上拉/下拉 (00)
    GPIOA_PUPDR &= ~(0xFU << (2 * 2));  // PA2无上拉/下拉 (00)
    GPIOA_PUPDR &= ~(0xFU << (3 * 2));  // PA3无上拉/下拉 (00)
}

// 点亮LED
void LED_Off(uint8_t pin) {
    GPIOA_BSRR = (1 << pin);  // 设置对应引脚高电平
}

// 熄灭LED
void LED_ON(uint8_t pin) {
    GPIOA_BSRR = (1 << (pin + 16));  // 设置对应引脚低电平
}


Archive member included to satisfy reference by file (symbol)

D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o (exit)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o) (__stdio_exit_handler)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (_fwalk_sglue)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (__sread)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o (memset)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_close_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o) (errno)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o) (_impure_ptr)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_lseek_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_read_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_write_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o (__libc_init_array)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (__retarget_lock_init_recursive)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o) (_free_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (_malloc_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o) (__malloc_lock)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (_fflush_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o) (_sbrk_r)

Discarded input sections

 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .rodata.all_implied_fbits
                0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .data.__dso_handle
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text          0x00000000       0x7c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .ARM.extab     0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .ARM.exidx     0x00000000       0x10 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .debug_line_str
                0x00000000       0xd6 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .ARM.attributes
                0x00000000       0x20 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .text.exit     0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .debug_frame   0x00000000       0x28 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.std      0x00000000       0x6c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.stdio_exit_handler
                0x00000000       0x18 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.cleanup_stdio
                0x00000000       0x40 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_lock
                0x00000000       0x18 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_unlock
                0x00000000       0x18 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.global_stdio_init.part.0
                0x00000000       0x40 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sfp_lock_acquire
                0x00000000        0xc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sfp_lock_release
                0x00000000        0xc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sfp    0x00000000       0xa4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sinit  0x00000000       0x30 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_lock_all
                0x00000000       0x1c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_unlock_all
                0x00000000       0x1c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .data.__sglue  0x00000000        0xc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .bss.__sf      0x00000000      0x138 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .bss.__stdio_exit_handler
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .debug_frame   0x00000000      0x144 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .text._fwalk_sglue
                0x00000000       0x3c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .debug_frame   0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__sread  0x00000000       0x22 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__seofread
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__swrite
                0x00000000       0x38 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__sseek  0x00000000       0x26 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__sclose
                0x00000000        0x8 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .debug_frame   0x00000000       0x88 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .text.memset   0x00000000       0x10 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .debug_frame   0x00000000       0x20 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .text._close_r
                0x00000000       0x20 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .text._reclaim_reent
                0x00000000       0xbc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .bss.errno     0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .debug_frame   0x00000000       0x38 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .data._impure_ptr
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .data._impure_data
                0x00000000       0x4c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .text._lseek_r
                0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .text._read_r  0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .text._write_r
                0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_init_recursive
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_acquire_recursive
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_release_recursive
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___arc4random_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___dd_hash_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___tz_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___env_recursive_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___malloc_recursive_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___at_quick_exit_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___atexit_recursive_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___sfp_recursive_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .debug_frame   0x00000000       0xb0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .text._free_r  0x00000000       0x94 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .debug_frame   0x00000000       0x38 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .text.sbrk_aligned
                0x00000000       0x44 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .text._malloc_r
                0x00000000      0x100 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .bss.__malloc_sbrk_start
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .bss.__malloc_free_list
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .debug_frame   0x00000000       0x50 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .text.__malloc_lock
                0x00000000        0xc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .text.__malloc_unlock
                0x00000000        0xc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .debug_frame   0x00000000       0x30 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text.__sflush_r
                0x00000000      0x104 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text._fflush_r
                0x00000000       0x50 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text.fflush   0x00000000       0x28 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .debug_frame   0x00000000       0x5c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .text._sbrk_r  0x00000000       0x20 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .text          0x00000000        0x0 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .data          0x00000000        0x0 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .bss           0x00000000        0x0 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .text          0x00000000        0x0 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .data          0x00000000        0x0 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .bss           0x00000000        0x0 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000      0xad2 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000      0x103 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x6a CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000      0x1df CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .text          0x00000000        0x0 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .data          0x00000000        0x0 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .bss           0x00000000        0x0 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000      0xad2 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000       0x8e CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000       0x51 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000      0x103 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000       0x6a CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000      0x1df CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .text          0x00000000       0x14 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj
 .data          0x00000000        0x0 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj
 .bss           0x00000000        0x0 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .rodata.all_implied_fbits
                0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .eh_frame      0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o

Memory Configuration

Name             Origin             Length             Attributes
RAM              0x20000000         0x00010000         xrw
FLASH            0x08000000         0x00040000         xr
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
START GROUP
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libm.a
END GROUP
LOAD CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
LOAD CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
LOAD CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
LOAD CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj
START GROUP
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libgcc.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libg_nano.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a
END GROUP
START GROUP
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libgcc.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a
END GROUP
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
                0x20010000                        _estack = (ORIGIN (RAM) + LENGTH (RAM))
                0x00000200                        _Min_Heap_Size = 0x200
                0x00000400                        _Min_Stack_Size = 0x400

.isr_vector     0x08000000      0x194
                0x08000000                        . = ALIGN (0x4)
 *(.isr_vector)
 .isr_vector    0x08000000      0x194 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj
                0x08000000                g_pfnVectors
                0x08000194                        . = ALIGN (0x4)

.text           0x08000194      0x2c0
                0x08000194                        . = ALIGN (0x4)
 *(.text)
 *(.text*)
 .text.deregister_tm_clones
                0x08000194       0x1c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text.register_tm_clones
                0x080001b0       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text.__do_global_dtors_aux
                0x080001d4       0x28 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text.frame_dummy
                0x080001fc       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text.__libc_init_array
                0x08000220       0x48 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
                0x08000220                __libc_init_array
 .text.GPIOA_Init
                0x08000268      0x108 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
                0x08000268                GPIOA_Init
 .text.LED_OFF  0x08000370       0x24 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
                0x08000370                LED_OFF
 .text.LED_ON   0x08000394       0x28 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
                0x08000394                LED_ON
 .text.main     0x080003bc       0x1e CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
                0x080003bc                main
 .text.SystemInit
                0x080003da        0xe CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
                0x080003da                SystemInit
 .text.Reset_Handler
                0x080003e8       0x50 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj
                0x080003e8                Reset_Handler
 .text.Default_Handler
                0x08000438        0x2 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj
                0x08000438                RTC_Alarm_IRQHandler
                0x08000438                EXTI2_IRQHandler
                0x08000438                DebugMon_Handler
                0x08000438                SPI4_IRQHandler
                0x08000438                TIM1_CC_IRQHandler
                0x08000438                DMA2_Stream5_IRQHandler
                0x08000438                HardFault_Handler
                0x08000438                DMA1_Stream5_IRQHandler
                0x08000438                SysTick_Handler
                0x08000438                PVD_IRQHandler
                0x08000438                SDIO_IRQHandler
                0x08000438                TAMP_STAMP_IRQHandler
                0x08000438                PendSV_Handler
                0x08000438                NMI_Handler
                0x08000438                EXTI3_IRQHandler
                0x08000438                TIM1_UP_TIM10_IRQHandler
                0x08000438                I2C3_ER_IRQHandler
                0x08000438                EXTI0_IRQHandler
                0x08000438                I2C2_EV_IRQHandler
                0x08000438                DMA1_Stream2_IRQHandler
                0x08000438                FPU_IRQHandler
                0x08000438                UsageFault_Handler
                0x08000438                DMA2_Stream2_IRQHandler
                0x08000438                SPI1_IRQHandler
                0x08000438                TIM1_BRK_TIM9_IRQHandler
                0x08000438                DMA2_Stream3_IRQHandler
                0x08000438                USART6_IRQHandler
                0x08000438                DMA2_Stream0_IRQHandler
                0x08000438                TIM4_IRQHandler
                0x08000438                I2C1_EV_IRQHandler
                0x08000438                DMA1_Stream6_IRQHandler
                0x08000438                DMA1_Stream1_IRQHandler
                0x08000438                TIM3_IRQHandler
                0x08000438                RCC_IRQHandler
                0x08000438                Default_Handler
                0x08000438                EXTI15_10_IRQHandler
                0x08000438                ADC_IRQHandler
                0x08000438                DMA1_Stream7_IRQHandler
                0x08000438                TIM5_IRQHandler
                0x08000438                DMA2_Stream7_IRQHandler
                0x08000438                I2C3_EV_IRQHandler
                0x08000438                EXTI9_5_IRQHandler
                0x08000438                RTC_WKUP_IRQHandler
                0x08000438                SPI2_IRQHandler
                0x08000438                MemManage_Handler
                0x08000438                DMA1_Stream0_IRQHandler
                0x08000438                SVC_Handler
                0x08000438                EXTI4_IRQHandler
                0x08000438                WWDG_IRQHandler
                0x08000438                TIM2_IRQHandler
                0x08000438                OTG_FS_WKUP_IRQHandler
                0x08000438                TIM1_TRG_COM_TIM11_IRQHandler
                0x08000438                EXTI1_IRQHandler
                0x08000438                USART2_IRQHandler
                0x08000438                I2C2_ER_IRQHandler
                0x08000438                DMA2_Stream1_IRQHandler
                0x08000438                FLASH_IRQHandler
                0x08000438                DMA2_Stream4_IRQHandler
                0x08000438                BusFault_Handler
                0x08000438                USART1_IRQHandler
                0x08000438                OTG_FS_IRQHandler
                0x08000438                SPI3_IRQHandler
                0x08000438                DMA1_Stream4_IRQHandler
                0x08000438                I2C1_ER_IRQHandler
                0x08000438                DMA2_Stream6_IRQHandler
                0x08000438                DMA1_Stream3_IRQHandler
 *(.glue_7)
 .glue_7        0x0800043a        0x0 linker stubs
 *(.glue_7t)
 .glue_7t       0x0800043a        0x0 linker stubs
 *(.eh_frame)
 *fill*         0x0800043a        0x2 
 .eh_frame      0x0800043c        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 *(.init)
 .init          0x0800043c        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
                0x0800043c                _init
 .init          0x08000440        0x8 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
 *(.fini)
 .fini          0x08000448        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
                0x08000448                _fini
 .fini          0x0800044c        0x8 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
                0x08000454                        . = ALIGN (0x4)
                0x08000454                        _etext = .

.vfp11_veneer   0x08000454        0x0
 .vfp11_veneer  0x08000454        0x0 linker stubs

.v4_bx          0x08000454        0x0
 .v4_bx         0x08000454        0x0 linker stubs

.iplt           0x08000454        0x0
 .iplt          0x08000454        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o

.rel.dyn        0x08000454        0x0
 .rel.iplt      0x08000454        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o

.rodata         0x08000454        0x0
                0x08000454                        . = ALIGN (0x4)
 *(.rodata)
 *(.rodata*)
                0x08000454                        . = ALIGN (0x4)

.ARM.extab
 *(.ARM.extab* .gnu.linkonce.armextab.*)

.ARM            0x08000454        0x0
                0x08000454                        __exidx_start = .
 *(.ARM.exidx*)
                0x08000454                        __exidx_end = .

.preinit_array  0x08000454        0x0
                0x08000454                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array*)
                0x08000454                        PROVIDE (__preinit_array_end = .)

.init_array     0x08000454        0x4
                0x08000454                        PROVIDE (__init_array_start = .)
 *(SORT_BY_NAME(.init_array.*))
 *(.init_array*)
 .init_array    0x08000454        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
                0x08000458                        PROVIDE (__init_array_end = .)

.fini_array     0x08000458        0x4
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_NAME(.fini_array.*))
 *(.fini_array*)
 .fini_array    0x08000458        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
                [!provide]                        PROVIDE (__fini_array_end = .)
                0x0800045c                        _sidata = LOADADDR (.data)

.data           0x20000000        0x0 load address 0x0800045c
                0x20000000                        . = ALIGN (0x4)
                0x20000000                        _sdata = .
 *(.data)
 *(.data*)
                0x20000000                        . = ALIGN (0x4)
                0x20000000                        _edata = .

.tm_clone_table
                0x20000000        0x0 load address 0x0800045c
 .tm_clone_table
                0x20000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .tm_clone_table
                0x20000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o

.igot.plt       0x20000000        0x0 load address 0x0800045c
 .igot.plt      0x20000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
                0x20000000                        . = ALIGN (0x4)

.bss            0x20000000       0x1c load address 0x0800045c
                0x20000000                        _sbss = .
                0x20000000                        __bss_start__ = _sbss
 *(.bss)
 *(.bss*)
 .bss.completed.1
                0x20000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 *fill*         0x20000001        0x3 
 .bss.object.0  0x20000004       0x18 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 *(COMMON)
                0x2000001c                        . = ALIGN (0x4)
                0x2000001c                        _ebss = .
                0x2000001c                        __bss_end__ = _ebss

._user_heap_stack
                0x2000001c      0x604 load address 0x0800045c
                0x20000020                        . = ALIGN (0x8)
 *fill*         0x2000001c        0x4 
                [!provide]                        PROVIDE (end = .)
                [!provide]                        PROVIDE (_end = .)
                0x20000220                        . = (. + _Min_Heap_Size)
 *fill*         0x20000020      0x200 
                0x20000620                        . = (. + _Min_Stack_Size)
 *fill*         0x20000220      0x400 
                0x20000620                        . = ALIGN (0x8)

/DISCARD/
 libc.a(*)
 libm.a(*)
 libgcc.a(*)

.ARM.attributes
                0x00000000       0x30
 *(.ARM.attributes)
 .ARM.attributes
                0x00000000       0x22 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
 .ARM.attributes
                0x00000022       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .ARM.attributes
                0x00000056       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .ARM.attributes
                0x0000008a       0x34 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .ARM.attributes
                0x000000be       0x34 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .ARM.attributes
                0x000000f2       0x34 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .ARM.attributes
                0x00000126       0x21 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj
 .ARM.attributes
                0x00000147       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .ARM.attributes
                0x0000017b       0x22 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
OUTPUT(D:\ELECT\CODE\vscode\ST\stm32f401ccu6\register_test\register_mode\output\write_cmake_test elf32-littlearm)
LOAD linker stubs
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libm.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libgcc.a

.debug_frame    0x00000000      0x108
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .debug_frame   0x0000002c       0x80 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_frame   0x000000ac       0x2c CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_frame   0x000000d8       0x30 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj

.debug_info     0x00000000      0x281
 .debug_info    0x00000000       0xf9 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_info    0x000000f9       0xd5 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_info    0x000001ce       0x83 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_info    0x00000251       0x30 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj

.debug_abbrev   0x00000000      0x173
 .debug_abbrev  0x00000000       0x83 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_abbrev  0x00000083       0x87 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_abbrev  0x0000010a       0x45 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_abbrev  0x0000014f       0x24 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj

.debug_aranges  0x00000000       0x98
 .debug_aranges
                0x00000000       0x30 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_aranges
                0x00000030       0x20 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_aranges
                0x00000050       0x20 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_aranges
                0x00000070       0x28 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj

.debug_rnglists
                0x00000000       0x5f
 .debug_rnglists
                0x00000000       0x20 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_rnglists
                0x00000020       0x13 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_rnglists
                0x00000033       0x13 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_rnglists
                0x00000046       0x19 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj

.debug_macro    0x00000000     0x1dc1
 .debug_macro   0x00000000       0x93 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000093      0xad2 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000b65       0x22 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000b87       0x8e CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000c15       0x51 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000c66      0x103 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000d69       0x6a CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000dd3      0x1df CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000fb2      0x17d CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x0000112f       0x75 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x000011a4       0x2a CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x000011ce       0x94 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001262       0x3c CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x0000129e       0x34 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x000012d2       0x16 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x000012e8       0x57 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x0000133f       0x9e CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x000013dd      0x364 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001741      0x112 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001853       0x10 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001863       0x16 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001879       0x4a CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x000018c3       0x34 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x000018f7       0x10 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001907       0x58 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x0000195f      0x1e5 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001b44       0x16 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001b5a       0x16 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001b70      0x170 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001ce0       0x16 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001cf6       0x3c CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001d32       0x22 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001d54       0x6d CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj

.debug_line     0x00000000      0xa5e
 .debug_line    0x00000000      0x317 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
 .debug_line    0x00000317      0x402 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_line    0x00000719      0x2cb CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_line    0x000009e4       0x7a CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj

.debug_str      0x00000000     0x74b6
 .debug_str     0x00000000     0x74b6 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
                               0x3f6d (size before relaxing)
 .debug_str     0x000074b6     0x73da CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .debug_str     0x000074b6     0x3dd1 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_str     0x000074b6       0xbc CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj

.comment        0x00000000       0x45
 .comment       0x00000000       0x45 CMakeFiles/write_cmake_test.dir/code/App/Src/appled.c.obj
                                 0x46 (size before relaxing)
 .comment       0x00000045       0x46 CMakeFiles/write_cmake_test.dir/code/User/Src/main.c.obj
 .comment       0x00000045       0x46 CMakeFiles/write_cmake_test.dir/code/User/Src/system_stm32f4xx.c.obj

.debug_line_str
                0x00000000       0xa0
 .debug_line_str
                0x00000000       0xa0 CMakeFiles/write_cmake_test.dir/startup_stm32f401xc.s.obj

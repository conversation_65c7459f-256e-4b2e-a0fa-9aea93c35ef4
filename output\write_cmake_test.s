
D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/output/write_cmake_test:     file format elf32-littlearm


Disassembly of section .text:

08000194 <deregister_tm_clones>:
 8000194:	4803      	ldr	r0, [pc, #12]	@ (80001a4 <deregister_tm_clones+0x10>)
 8000196:	4b04      	ldr	r3, [pc, #16]	@ (80001a8 <deregister_tm_clones+0x14>)
 8000198:	4283      	cmp	r3, r0
 800019a:	d002      	beq.n	80001a2 <deregister_tm_clones+0xe>
 800019c:	4b03      	ldr	r3, [pc, #12]	@ (80001ac <deregister_tm_clones+0x18>)
 800019e:	b103      	cbz	r3, 80001a2 <deregister_tm_clones+0xe>
 80001a0:	4718      	bx	r3
 80001a2:	4770      	bx	lr
 80001a4:	20000000 	.word	0x20000000
 80001a8:	20000000 	.word	0x20000000
 80001ac:	00000000 	.word	0x00000000

080001b0 <register_tm_clones>:
 80001b0:	4805      	ldr	r0, [pc, #20]	@ (80001c8 <register_tm_clones+0x18>)
 80001b2:	4b06      	ldr	r3, [pc, #24]	@ (80001cc <register_tm_clones+0x1c>)
 80001b4:	1a1b      	subs	r3, r3, r0
 80001b6:	0fd9      	lsrs	r1, r3, #31
 80001b8:	eb01 01a3 	add.w	r1, r1, r3, asr #2
 80001bc:	1049      	asrs	r1, r1, #1
 80001be:	d002      	beq.n	80001c6 <register_tm_clones+0x16>
 80001c0:	4b03      	ldr	r3, [pc, #12]	@ (80001d0 <register_tm_clones+0x20>)
 80001c2:	b103      	cbz	r3, 80001c6 <register_tm_clones+0x16>
 80001c4:	4718      	bx	r3
 80001c6:	4770      	bx	lr
 80001c8:	20000000 	.word	0x20000000
 80001cc:	20000000 	.word	0x20000000
 80001d0:	00000000 	.word	0x00000000

080001d4 <__do_global_dtors_aux>:
 80001d4:	b510      	push	{r4, lr}
 80001d6:	4c06      	ldr	r4, [pc, #24]	@ (80001f0 <__do_global_dtors_aux+0x1c>)
 80001d8:	7823      	ldrb	r3, [r4, #0]
 80001da:	b943      	cbnz	r3, 80001ee <__do_global_dtors_aux+0x1a>
 80001dc:	f7ff ffda 	bl	8000194 <deregister_tm_clones>
 80001e0:	4b04      	ldr	r3, [pc, #16]	@ (80001f4 <__do_global_dtors_aux+0x20>)
 80001e2:	b113      	cbz	r3, 80001ea <__do_global_dtors_aux+0x16>
 80001e4:	4804      	ldr	r0, [pc, #16]	@ (80001f8 <__do_global_dtors_aux+0x24>)
 80001e6:	f3af 8000 	nop.w
 80001ea:	2301      	movs	r3, #1
 80001ec:	7023      	strb	r3, [r4, #0]
 80001ee:	bd10      	pop	{r4, pc}
 80001f0:	20000000 	.word	0x20000000
 80001f4:	00000000 	.word	0x00000000
 80001f8:	0800043c 	.word	0x0800043c

080001fc <frame_dummy>:
 80001fc:	b508      	push	{r3, lr}
 80001fe:	4b05      	ldr	r3, [pc, #20]	@ (8000214 <frame_dummy+0x18>)
 8000200:	b11b      	cbz	r3, 800020a <frame_dummy+0xe>
 8000202:	4905      	ldr	r1, [pc, #20]	@ (8000218 <frame_dummy+0x1c>)
 8000204:	4805      	ldr	r0, [pc, #20]	@ (800021c <frame_dummy+0x20>)
 8000206:	f3af 8000 	nop.w
 800020a:	e8bd 4008 	ldmia.w	sp!, {r3, lr}
 800020e:	f7ff bfcf 	b.w	80001b0 <register_tm_clones>
 8000212:	bf00      	nop
 8000214:	00000000 	.word	0x00000000
 8000218:	20000004 	.word	0x20000004
 800021c:	0800043c 	.word	0x0800043c

08000220 <__libc_init_array>:
 8000220:	b570      	push	{r4, r5, r6, lr}
 8000222:	4b0d      	ldr	r3, [pc, #52]	@ (8000258 <__libc_init_array+0x38>)
 8000224:	4d0d      	ldr	r5, [pc, #52]	@ (800025c <__libc_init_array+0x3c>)
 8000226:	1b5b      	subs	r3, r3, r5
 8000228:	109c      	asrs	r4, r3, #2
 800022a:	2600      	movs	r6, #0
 800022c:	42a6      	cmp	r6, r4
 800022e:	d109      	bne.n	8000244 <__libc_init_array+0x24>
 8000230:	f000 f904 	bl	800043c <_init>
 8000234:	4d0a      	ldr	r5, [pc, #40]	@ (8000260 <__libc_init_array+0x40>)
 8000236:	4b0b      	ldr	r3, [pc, #44]	@ (8000264 <__libc_init_array+0x44>)
 8000238:	1b5b      	subs	r3, r3, r5
 800023a:	109c      	asrs	r4, r3, #2
 800023c:	2600      	movs	r6, #0
 800023e:	42a6      	cmp	r6, r4
 8000240:	d105      	bne.n	800024e <__libc_init_array+0x2e>
 8000242:	bd70      	pop	{r4, r5, r6, pc}
 8000244:	f855 3b04 	ldr.w	r3, [r5], #4
 8000248:	4798      	blx	r3
 800024a:	3601      	adds	r6, #1
 800024c:	e7ee      	b.n	800022c <__libc_init_array+0xc>
 800024e:	f855 3b04 	ldr.w	r3, [r5], #4
 8000252:	4798      	blx	r3
 8000254:	3601      	adds	r6, #1
 8000256:	e7f2      	b.n	800023e <__libc_init_array+0x1e>
 8000258:	08000454 	.word	0x08000454
 800025c:	08000454 	.word	0x08000454
 8000260:	08000454 	.word	0x08000454
 8000264:	08000458 	.word	0x08000458

08000268 <GPIOA_Init>:
 8000268:	b480      	push	{r7}
 800026a:	af00      	add	r7, sp, #0
 800026c:	4b3b      	ldr	r3, [pc, #236]	@ (800035c <GPIOA_Init+0xf4>)
 800026e:	681b      	ldr	r3, [r3, #0]
 8000270:	4a3a      	ldr	r2, [pc, #232]	@ (800035c <GPIOA_Init+0xf4>)
 8000272:	f043 0301 	orr.w	r3, r3, #1
 8000276:	6013      	str	r3, [r2, #0]
 8000278:	4b39      	ldr	r3, [pc, #228]	@ (8000360 <GPIOA_Init+0xf8>)
 800027a:	681b      	ldr	r3, [r3, #0]
 800027c:	4a38      	ldr	r2, [pc, #224]	@ (8000360 <GPIOA_Init+0xf8>)
 800027e:	f023 030f 	bic.w	r3, r3, #15
 8000282:	6013      	str	r3, [r2, #0]
 8000284:	4b36      	ldr	r3, [pc, #216]	@ (8000360 <GPIOA_Init+0xf8>)
 8000286:	681b      	ldr	r3, [r3, #0]
 8000288:	4a35      	ldr	r2, [pc, #212]	@ (8000360 <GPIOA_Init+0xf8>)
 800028a:	f043 0301 	orr.w	r3, r3, #1
 800028e:	6013      	str	r3, [r2, #0]
 8000290:	4b33      	ldr	r3, [pc, #204]	@ (8000360 <GPIOA_Init+0xf8>)
 8000292:	681b      	ldr	r3, [r3, #0]
 8000294:	4a32      	ldr	r2, [pc, #200]	@ (8000360 <GPIOA_Init+0xf8>)
 8000296:	f023 03f0 	bic.w	r3, r3, #240	@ 0xf0
 800029a:	6013      	str	r3, [r2, #0]
 800029c:	4b30      	ldr	r3, [pc, #192]	@ (8000360 <GPIOA_Init+0xf8>)
 800029e:	681b      	ldr	r3, [r3, #0]
 80002a0:	4a2f      	ldr	r2, [pc, #188]	@ (8000360 <GPIOA_Init+0xf8>)
 80002a2:	f043 0310 	orr.w	r3, r3, #16
 80002a6:	6013      	str	r3, [r2, #0]
 80002a8:	4b2d      	ldr	r3, [pc, #180]	@ (8000360 <GPIOA_Init+0xf8>)
 80002aa:	681b      	ldr	r3, [r3, #0]
 80002ac:	4a2c      	ldr	r2, [pc, #176]	@ (8000360 <GPIOA_Init+0xf8>)
 80002ae:	f423 7370 	bic.w	r3, r3, #960	@ 0x3c0
 80002b2:	6013      	str	r3, [r2, #0]
 80002b4:	4b2a      	ldr	r3, [pc, #168]	@ (8000360 <GPIOA_Init+0xf8>)
 80002b6:	681b      	ldr	r3, [r3, #0]
 80002b8:	4a29      	ldr	r2, [pc, #164]	@ (8000360 <GPIOA_Init+0xf8>)
 80002ba:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 80002be:	6013      	str	r3, [r2, #0]
 80002c0:	4b28      	ldr	r3, [pc, #160]	@ (8000364 <GPIOA_Init+0xfc>)
 80002c2:	681b      	ldr	r3, [r3, #0]
 80002c4:	4a27      	ldr	r2, [pc, #156]	@ (8000364 <GPIOA_Init+0xfc>)
 80002c6:	f023 0301 	bic.w	r3, r3, #1
 80002ca:	6013      	str	r3, [r2, #0]
 80002cc:	4b25      	ldr	r3, [pc, #148]	@ (8000364 <GPIOA_Init+0xfc>)
 80002ce:	681b      	ldr	r3, [r3, #0]
 80002d0:	4a24      	ldr	r2, [pc, #144]	@ (8000364 <GPIOA_Init+0xfc>)
 80002d2:	f023 0304 	bic.w	r3, r3, #4
 80002d6:	6013      	str	r3, [r2, #0]
 80002d8:	4b22      	ldr	r3, [pc, #136]	@ (8000364 <GPIOA_Init+0xfc>)
 80002da:	681b      	ldr	r3, [r3, #0]
 80002dc:	4a21      	ldr	r2, [pc, #132]	@ (8000364 <GPIOA_Init+0xfc>)
 80002de:	f023 0308 	bic.w	r3, r3, #8
 80002e2:	6013      	str	r3, [r2, #0]
 80002e4:	4b20      	ldr	r3, [pc, #128]	@ (8000368 <GPIOA_Init+0x100>)
 80002e6:	681b      	ldr	r3, [r3, #0]
 80002e8:	4a1f      	ldr	r2, [pc, #124]	@ (8000368 <GPIOA_Init+0x100>)
 80002ea:	f023 030f 	bic.w	r3, r3, #15
 80002ee:	6013      	str	r3, [r2, #0]
 80002f0:	4b1d      	ldr	r3, [pc, #116]	@ (8000368 <GPIOA_Init+0x100>)
 80002f2:	681b      	ldr	r3, [r3, #0]
 80002f4:	4a1c      	ldr	r2, [pc, #112]	@ (8000368 <GPIOA_Init+0x100>)
 80002f6:	f043 0301 	orr.w	r3, r3, #1
 80002fa:	6013      	str	r3, [r2, #0]
 80002fc:	4b1a      	ldr	r3, [pc, #104]	@ (8000368 <GPIOA_Init+0x100>)
 80002fe:	681b      	ldr	r3, [r3, #0]
 8000300:	4a19      	ldr	r2, [pc, #100]	@ (8000368 <GPIOA_Init+0x100>)
 8000302:	f023 03f0 	bic.w	r3, r3, #240	@ 0xf0
 8000306:	6013      	str	r3, [r2, #0]
 8000308:	4b17      	ldr	r3, [pc, #92]	@ (8000368 <GPIOA_Init+0x100>)
 800030a:	681b      	ldr	r3, [r3, #0]
 800030c:	4a16      	ldr	r2, [pc, #88]	@ (8000368 <GPIOA_Init+0x100>)
 800030e:	f043 0310 	orr.w	r3, r3, #16
 8000312:	6013      	str	r3, [r2, #0]
 8000314:	4b14      	ldr	r3, [pc, #80]	@ (8000368 <GPIOA_Init+0x100>)
 8000316:	681b      	ldr	r3, [r3, #0]
 8000318:	4a13      	ldr	r2, [pc, #76]	@ (8000368 <GPIOA_Init+0x100>)
 800031a:	f423 7370 	bic.w	r3, r3, #960	@ 0x3c0
 800031e:	6013      	str	r3, [r2, #0]
 8000320:	4b11      	ldr	r3, [pc, #68]	@ (8000368 <GPIOA_Init+0x100>)
 8000322:	681b      	ldr	r3, [r3, #0]
 8000324:	4a10      	ldr	r2, [pc, #64]	@ (8000368 <GPIOA_Init+0x100>)
 8000326:	f043 0340 	orr.w	r3, r3, #64	@ 0x40
 800032a:	6013      	str	r3, [r2, #0]
 800032c:	4b0f      	ldr	r3, [pc, #60]	@ (800036c <GPIOA_Init+0x104>)
 800032e:	681b      	ldr	r3, [r3, #0]
 8000330:	4a0e      	ldr	r2, [pc, #56]	@ (800036c <GPIOA_Init+0x104>)
 8000332:	f023 030f 	bic.w	r3, r3, #15
 8000336:	6013      	str	r3, [r2, #0]
 8000338:	4b0c      	ldr	r3, [pc, #48]	@ (800036c <GPIOA_Init+0x104>)
 800033a:	681b      	ldr	r3, [r3, #0]
 800033c:	4a0b      	ldr	r2, [pc, #44]	@ (800036c <GPIOA_Init+0x104>)
 800033e:	f023 03f0 	bic.w	r3, r3, #240	@ 0xf0
 8000342:	6013      	str	r3, [r2, #0]
 8000344:	4b09      	ldr	r3, [pc, #36]	@ (800036c <GPIOA_Init+0x104>)
 8000346:	681b      	ldr	r3, [r3, #0]
 8000348:	4a08      	ldr	r2, [pc, #32]	@ (800036c <GPIOA_Init+0x104>)
 800034a:	f423 7370 	bic.w	r3, r3, #960	@ 0x3c0
 800034e:	6013      	str	r3, [r2, #0]
 8000350:	bf00      	nop
 8000352:	46bd      	mov	sp, r7
 8000354:	f85d 7b04 	ldr.w	r7, [sp], #4
 8000358:	4770      	bx	lr
 800035a:	bf00      	nop
 800035c:	40023830 	.word	0x40023830
 8000360:	40020000 	.word	0x40020000
 8000364:	40020004 	.word	0x40020004
 8000368:	40020008 	.word	0x40020008
 800036c:	4002000c 	.word	0x4002000c

08000370 <LED_OFF>:
 8000370:	b480      	push	{r7}
 8000372:	b083      	sub	sp, #12
 8000374:	af00      	add	r7, sp, #0
 8000376:	4603      	mov	r3, r0
 8000378:	71fb      	strb	r3, [r7, #7]
 800037a:	79fb      	ldrb	r3, [r7, #7]
 800037c:	2201      	movs	r2, #1
 800037e:	409a      	lsls	r2, r3
 8000380:	4b03      	ldr	r3, [pc, #12]	@ (8000390 <LED_OFF+0x20>)
 8000382:	601a      	str	r2, [r3, #0]
 8000384:	bf00      	nop
 8000386:	370c      	adds	r7, #12
 8000388:	46bd      	mov	sp, r7
 800038a:	f85d 7b04 	ldr.w	r7, [sp], #4
 800038e:	4770      	bx	lr
 8000390:	40020018 	.word	0x40020018

08000394 <LED_ON>:
 8000394:	b480      	push	{r7}
 8000396:	b083      	sub	sp, #12
 8000398:	af00      	add	r7, sp, #0
 800039a:	4603      	mov	r3, r0
 800039c:	71fb      	strb	r3, [r7, #7]
 800039e:	79fb      	ldrb	r3, [r7, #7]
 80003a0:	3310      	adds	r3, #16
 80003a2:	2201      	movs	r2, #1
 80003a4:	409a      	lsls	r2, r3
 80003a6:	4b04      	ldr	r3, [pc, #16]	@ (80003b8 <LED_ON+0x24>)
 80003a8:	601a      	str	r2, [r3, #0]
 80003aa:	bf00      	nop
 80003ac:	370c      	adds	r7, #12
 80003ae:	46bd      	mov	sp, r7
 80003b0:	f85d 7b04 	ldr.w	r7, [sp], #4
 80003b4:	4770      	bx	lr
 80003b6:	bf00      	nop
 80003b8:	40020018 	.word	0x40020018

080003bc <main>:
 80003bc:	b580      	push	{r7, lr}
 80003be:	af00      	add	r7, sp, #0
 80003c0:	f7ff ff52 	bl	8000268 <GPIOA_Init>
 80003c4:	2000      	movs	r0, #0
 80003c6:	f7ff ffe5 	bl	8000394 <LED_ON>
 80003ca:	2002      	movs	r0, #2
 80003cc:	f7ff ffd0 	bl	8000370 <LED_OFF>
 80003d0:	2003      	movs	r0, #3
 80003d2:	f7ff ffcd 	bl	8000370 <LED_OFF>
 80003d6:	bf00      	nop
 80003d8:	e7f4      	b.n	80003c4 <main+0x8>

080003da <SystemInit>:
 80003da:	b480      	push	{r7}
 80003dc:	af00      	add	r7, sp, #0
 80003de:	bf00      	nop
 80003e0:	46bd      	mov	sp, r7
 80003e2:	f85d 7b04 	ldr.w	r7, [sp], #4
 80003e6:	4770      	bx	lr

080003e8 <Reset_Handler>:
 80003e8:	f8df d034 	ldr.w	sp, [pc, #52]	@ 8000420 <LoopFillZerobss+0xe>
 80003ec:	f7ff fff5 	bl	80003da <SystemInit>
 80003f0:	480c      	ldr	r0, [pc, #48]	@ (8000424 <LoopFillZerobss+0x12>)
 80003f2:	490d      	ldr	r1, [pc, #52]	@ (8000428 <LoopFillZerobss+0x16>)
 80003f4:	4a0d      	ldr	r2, [pc, #52]	@ (800042c <LoopFillZerobss+0x1a>)
 80003f6:	2300      	movs	r3, #0
 80003f8:	e002      	b.n	8000400 <LoopCopyDataInit>

080003fa <CopyDataInit>:
 80003fa:	58d4      	ldr	r4, [r2, r3]
 80003fc:	50c4      	str	r4, [r0, r3]
 80003fe:	3304      	adds	r3, #4

08000400 <LoopCopyDataInit>:
 8000400:	18c4      	adds	r4, r0, r3
 8000402:	428c      	cmp	r4, r1
 8000404:	d3f9      	bcc.n	80003fa <CopyDataInit>
 8000406:	4a0a      	ldr	r2, [pc, #40]	@ (8000430 <LoopFillZerobss+0x1e>)
 8000408:	4c0a      	ldr	r4, [pc, #40]	@ (8000434 <LoopFillZerobss+0x22>)
 800040a:	2300      	movs	r3, #0
 800040c:	e001      	b.n	8000412 <LoopFillZerobss>

0800040e <FillZerobss>:
 800040e:	6013      	str	r3, [r2, #0]
 8000410:	3204      	adds	r2, #4

08000412 <LoopFillZerobss>:
 8000412:	42a2      	cmp	r2, r4
 8000414:	d3fb      	bcc.n	800040e <FillZerobss>
 8000416:	f7ff ff03 	bl	8000220 <__libc_init_array>
 800041a:	f7ff ffcf 	bl	80003bc <main>
 800041e:	4770      	bx	lr
 8000420:	20010000 	.word	0x20010000
 8000424:	20000000 	.word	0x20000000
 8000428:	20000000 	.word	0x20000000
 800042c:	0800045c 	.word	0x0800045c
 8000430:	20000000 	.word	0x20000000
 8000434:	2000001c 	.word	0x2000001c

08000438 <ADC_IRQHandler>:
 8000438:	e7fe      	b.n	8000438 <ADC_IRQHandler>
	...

0800043c <_init>:
 800043c:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 800043e:	bf00      	nop
 8000440:	bcf8      	pop	{r3, r4, r5, r6, r7}
 8000442:	bc08      	pop	{r3}
 8000444:	469e      	mov	lr, r3
 8000446:	4770      	bx	lr

08000448 <_fini>:
 8000448:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 800044a:	bf00      	nop
 800044c:	bcf8      	pop	{r3, r4, r5, r6, r7}
 800044e:	bc08      	pop	{r3}
 8000450:	469e      	mov	lr, r3
 8000452:	4770      	bx	lr
